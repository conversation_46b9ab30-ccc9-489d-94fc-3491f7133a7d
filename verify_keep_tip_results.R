# 验证使用keep.tip()函数生成的小树结果

library(ape)

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

cat("验证使用ape包keep.tip()函数生成的小树结果\n")
cat(paste(rep("=", 60), collapse = ""), "\n\n")

# 分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  subtree_file <- paste0(class_name, "_subtree.tre")
  
  cat("验证", class_name, "小树:\n")
  
  # 读取原始分类数据
  if (file.exists(class_file)) {
    class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
    expected_families <- sort(unique(class_data$wcvp_family))
    
    cat("  - 预期科数:", length(expected_families), "\n")
    
    # 读取小树
    if (file.exists(subtree_file)) {
      subtree <- read.tree(subtree_file)
      actual_families <- sort(subtree$tip.label)
      
      cat("  - 小树科数:", length(actual_families), "\n")
      
      # 验证是否完全匹配
      if (identical(expected_families, actual_families)) {
        cat("  ✓ 小树包含所有预期的科，完全匹配\n")
      } else {
        cat("  ✗ 小树与预期不匹配\n")
        
        # 检查缺失的科
        missing <- setdiff(expected_families, actual_families)
        if (length(missing) > 0) {
          cat("    缺失的科:", paste(missing, collapse = ", "), "\n")
        }
        
        # 检查多余的科
        extra <- setdiff(actual_families, expected_families)
        if (length(extra) > 0) {
          cat("    多余的科:", paste(extra, collapse = ", "), "\n")
        }
      }
      
      # 显示树的基本信息
      cat("  - 末端节点数:", Ntip(subtree), "\n")
      cat("  - 内部节点数:", Nnode(subtree), "\n")
      
    } else {
      cat("  ✗ 小树文件不存在:", subtree_file, "\n")
    }
  } else {
    cat("  ✗ 分类文件不存在:", class_file, "\n")
  }
  
  cat("\n")
}

cat("验证完成！\n")
