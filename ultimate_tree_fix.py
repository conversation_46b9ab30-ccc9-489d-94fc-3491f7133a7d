#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极树文件修复工具
彻底解决IQ-TREE的"冗余双重括号"问题
"""

import re
import os

def extract_species_from_any_tree(tree_string):
    """
    从任何格式的树中提取物种名称
    """
    # 多种物种名称模式
    patterns = [
        r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b',  # 标准格式 Genus_species
        r'\b([A-Z][a-z]+_[A-Z][a-z]+)\b',         # Genus_Species
        r'\b([A-Z][a-z]+_[a-z]+_[a-z]+)\b',       # Genus_species_subspecies
    ]
    
    all_species = set()
    for pattern in patterns:
        matches = re.findall(pattern, tree_string)
        all_species.update(matches)
    
    return sorted(list(all_species))

def create_ultra_clean_tree(species_list, tree_name):
    """
    创建绝对干净的树格式，保证IQ-TREE兼容
    """
    print(f"为 {tree_name} 创建超级干净的树...")
    print(f"物种数: {len(species_list)}")
    
    if len(species_list) == 0:
        return "();"
    elif len(species_list) == 1:
        return f"{species_list[0]};"
    elif len(species_list) == 2:
        return f"({species_list[0]},{species_list[1]});"
    
    # 创建最简单的星形树 - 绝对不会有格式问题
    clean_tree = "(" + ",".join(species_list) + ");"
    
    # 验证
    open_count = clean_tree.count('(')
    close_count = clean_tree.count(')')
    
    print(f"  生成的树:")
    print(f"    长度: {len(clean_tree)} 字符")
    print(f"    开括号: {open_count}")
    print(f"    关括号: {close_count}")
    print(f"    括号平衡: {'是' if open_count == close_count else '否'}")
    
    return clean_tree

def create_minimal_binary_tree(species_list, tree_name):
    """
    创建最小的二分树结构
    """
    print(f"为 {tree_name} 创建最小二分树...")
    
    if len(species_list) <= 3:
        return create_ultra_clean_tree(species_list, tree_name)
    
    # 将物种分成两组
    mid = len(species_list) // 2
    group1 = species_list[:mid]
    group2 = species_list[mid:]
    
    # 创建两个分组
    left_part = ",".join(group1)
    right_part = ",".join(group2)
    
    # 构建二分树
    binary_tree = f"(({left_part}),({right_part}));"
    
    # 验证
    open_count = binary_tree.count('(')
    close_count = binary_tree.count(')')
    
    print(f"  二分树:")
    print(f"    左组: {len(group1)} 物种")
    print(f"    右组: {len(group2)} 物种")
    print(f"    开括号: {open_count}")
    print(f"    关括号: {close_count}")
    
    return binary_tree

def fix_all_trees_ultimate():
    """
    终极修复所有树文件
    """
    tree_files = [
        "ANA_species_tree.tre",
        "dicots_species_tree.tre", 
        "monocot_species_tree.tre",
        "superrosids_species_tree.tre",
        "superasterids_species_tree.tre"
    ]
    
    print("🔧 终极树文件修复工具")
    print("目标: 创建100%兼容IQ-TREE的树文件")
    print("=" * 80)
    
    success_count = 0
    
    for tree_file in tree_files:
        if not os.path.exists(tree_file):
            print(f"\n⚠️ 文件不存在: {tree_file}")
            continue
            
        print(f"\n处理文件: {tree_file}")
        print("-" * 60)
        
        try:
            # 读取原始树
            with open(tree_file, 'r', encoding='utf-8') as f:
                original_tree = f.read().strip()
            
            print(f"原始文件大小: {len(original_tree)} 字符")
            
            # 提取物种
            species_list = extract_species_from_any_tree(original_tree)
            
            if not species_list:
                print("❌ 未能提取到物种名称")
                continue
            
            print(f"提取到 {len(species_list)} 个物种")
            print(f"前5个物种: {species_list[:5]}")
            
            # 生成文件名
            base_name = tree_file.replace('_species_tree.tre', '')
            
            # 方案1: 超级干净的星形树
            clean_tree = create_ultra_clean_tree(species_list, base_name)
            clean_output = f"{base_name}_CLEAN.tre"
            
            with open(clean_output, 'w', encoding='utf-8') as f:
                f.write(clean_tree)
            
            print(f"✅ 超级干净树: {clean_output}")
            
            # 方案2: 最小二分树
            binary_tree = create_minimal_binary_tree(species_list, base_name)
            binary_output = f"{base_name}_BINARY.tre"
            
            with open(binary_output, 'w', encoding='utf-8') as f:
                f.write(binary_tree)
            
            print(f"✅ 最小二分树: {binary_output}")
            
            # 方案3: 测试用小树（前10个物种）
            if len(species_list) > 10:
                test_species = species_list[:10]
                test_tree = create_ultra_clean_tree(test_species, f"{base_name}_test")
                test_output = f"{base_name}_TEST.tre"
                
                with open(test_output, 'w', encoding='utf-8') as f:
                    f.write(test_tree)
                
                print(f"✅ 测试树: {test_output} ({len(test_species)} 物种)")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
    
    print(f"\n" + "=" * 80)
    print(f"🎉 处理完成: {success_count}/{len(tree_files)} 个文件")
    
    if success_count > 0:
        print(f"\n💡 使用建议:")
        print(f"1. 首先尝试 *_CLEAN.tre (星形树，最兼容)")
        print(f"2. 如果需要结构，尝试 *_BINARY.tre (二分树)")
        print(f"3. 用 *_TEST.tre 先验证IQ-TREE设置")
        print(f"4. 如果都失败，完全不使用约束树")
        
        print(f"\n📝 IQ-TREE命令:")
        print(f"# 最推荐 - 星形树:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_CLEAN.tre -m MFP -bb 1000")
        print(f"")
        print(f"# 备选 - 二分树:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_BINARY.tre -m MFP -bb 1000")
        print(f"")
        print(f"# 测试:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_TEST.tre -m MFP -bb 1000")
        print(f"")
        print(f"# 最后选择 - 无约束:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -m MFP -bb 1000")
    
    return success_count > 0

def main():
    """主函数"""
    try:
        fix_all_trees_ultimate()
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
