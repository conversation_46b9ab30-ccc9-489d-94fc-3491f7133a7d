#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证分类结果的正确性
"""

import pandas as pd
import os

def main():
    # 读取原始映射关系
    class_df = pd.read_csv('class.txt', sep='\t')
    order_to_class = {}
    for _, row in class_df.iterrows():
        if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
            order_to_class[row.iloc[0]] = row.iloc[1]
    
    print("Order到Class的映射关系:")
    for order, class_name in sorted(order_to_class.items()):
        print(f"  {order} -> {class_name}")
    
    print(f"\n总共有 {len(order_to_class)} 个映射关系")
    
    # 检查生成的文件
    class_files = ['ANA.txt', 'monocot.txt', 'dicots.txt', 'superrosids.txt', 'superasterids.txt']
    
    print(f"\n检查生成的分类文件:")
    total_rows = 0
    
    for filename in class_files:
        if os.path.exists(filename):
            df = pd.read_csv(filename, sep='\t')
            print(f"\n{filename}:")
            print(f"  行数: {len(df)}")
            
            # 统计该文件中的order分布
            order_counts = df['accepted_Order'].value_counts()
            print(f"  包含的orders:")
            for order, count in order_counts.items():
                expected_class = order_to_class.get(order, "未知")
                class_from_filename = filename.replace('.txt', '')
                status = "✓" if expected_class == class_from_filename else "✗"
                print(f"    {order}: {count} 行 -> {expected_class} {status}")
            
            total_rows += len(df)
    
    print(f"\n所有分类文件总行数: {total_rows}")
    
    # 读取原始gs_in_tree.txt文件进行对比
    original_df = pd.read_csv('gs_in_tree.txt', sep='\t')
    print(f"原始gs_in_tree.txt行数: {len(original_df)}")
    
    if total_rows == len(original_df) - 1:  # 减1是因为不包括表头
        print("✓ 所有数据都已正确分类!")
    else:
        print(f"✗ 数据行数不匹配，可能有数据丢失")

if __name__ == "__main__":
    main()
