import os
import shutil
from Bio import SeqIO

def merge_fasta_files():
    """
    合并当前路径和filtered_fasta250629文件夹中名称相同的FASTA文件
    """
    # 定义路径
    current_dir = "."  # 当前路径
    filtered_dir = "filtered_fasta250629"  # 过滤后的文件夹
    output_dir = "250629"  # 输出文件夹

    print("FASTA文件合并工具")
    print("=" * 60)
    print(f"当前路径: {os.path.abspath(current_dir)}")
    print(f"过滤文件夹: {os.path.abspath(filtered_dir)}")
    print(f"输出文件夹: {os.path.abspath(output_dir)}")
    print("=" * 60)

    # 检查filtered_fasta250629文件夹是否存在
    if not os.path.exists(filtered_dir):
        print(f"❌ 错误: {filtered_dir} 文件夹不存在！")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"✓ 输出目录已创建: {output_dir}")
    
    # 获取两个文件夹中的FASTA文件列表
    current_files = {f for f in os.listdir(current_dir)
                    if f.endswith('.fasta') and os.path.isfile(os.path.join(current_dir, f))}
    filtered_files = {f for f in os.listdir(filtered_dir) if f.endswith('.fasta')}

    print(f"\n当前路径中的FASTA文件数: {len(current_files)}")
    print(f"filtered_fasta250629文件夹中的FASTA文件数: {len(filtered_files)}")

    # 找到两个文件夹中名称相同的文件
    common_files = current_files.intersection(filtered_files)
    print(f"名称相同的文件数: {len(common_files)}")

    if len(common_files) == 0:
        print("⚠️ 警告: 没有找到名称相同的文件")
        return
    
    # 统计变量
    successful_merges = 0
    failed_merges = 0
    total_sequences_merged = 0
    
    print(f"\n开始合并文件...")
    print("-" * 60)
    
    # 遍历每个相同名称的文件
    for filename in sorted(common_files):
        try:
            print(f"\n处理文件: {filename}")
            
            # 构建文件路径
            current_file_path = os.path.join(current_dir, filename)
            filtered_file_path = os.path.join(filtered_dir, filename)
            output_file_path = os.path.join(output_dir, filename)

            # 统计每个文件的序列数
            current_sequences = list(SeqIO.parse(current_file_path, "fasta"))
            filtered_sequences = list(SeqIO.parse(filtered_file_path, "fasta"))

            print(f"  当前路径中的序列数: {len(current_sequences)}")
            print(f"  filtered_fasta250629中的序列数: {len(filtered_sequences)}")

            # 合并序列到输出文件
            with open(output_file_path, "w") as output_handle:
                # 先写入当前路径中的序列
                for record in current_sequences:
                    SeqIO.write(record, output_handle, "fasta")

                # 再写入filtered_fasta250629中的序列
                for record in filtered_sequences:
                    SeqIO.write(record, output_handle, "fasta")

            total_sequences_in_output = len(current_sequences) + len(filtered_sequences)
            total_sequences_merged += total_sequences_in_output
            
            print(f"  ✓ 合并完成，输出序列数: {total_sequences_in_output}")
            successful_merges += 1
            
        except Exception as e:
            print(f"  ❌ 合并失败: {str(e)}")
            failed_merges += 1
    
    # 输出最终统计结果
    print("\n" + "=" * 60)
    print("合并完成！最终统计:")
    print("=" * 60)
    print(f"成功合并的文件数: {successful_merges}")
    print(f"合并失败的文件数: {failed_merges}")
    print(f"合并的序列总数: {total_sequences_merged}")
    
    if successful_merges > 0:
        print(f"\n✓ 合并后的文件已保存到: {output_dir}")
        
        # 显示输出文件夹中的文件列表
        output_files = [f for f in os.listdir(output_dir) if f.endswith('.fasta')]
        print(f"输出文件夹中的文件数: {len(output_files)}")
        
        if len(output_files) <= 10:
            print("输出的文件列表:")
            for f in sorted(output_files):
                print(f"  - {f}")
        else:
            print("输出的文件列表（前10个）:")
            for f in sorted(output_files)[:10]:
                print(f"  - {f}")
            print(f"  ... 还有 {len(output_files) - 10} 个文件")
    
    # 检查是否有文件只在一个文件夹中存在
    only_in_current = current_files - filtered_files
    only_in_filtered = filtered_files - current_files

    if only_in_current:
        print(f"\n⚠️ 只在当前路径中存在的文件数: {len(only_in_current)}")
        if len(only_in_current) <= 5:
            print("文件列表:")
            for f in sorted(only_in_current):
                print(f"  - {f}")

    if only_in_filtered:
        print(f"\n⚠️ 只在filtered_fasta250629中存在的文件数: {len(only_in_filtered)}")
        if len(only_in_filtered) <= 5:
            print("文件列表:")
            for f in sorted(only_in_filtered):
                print(f"  - {f}")

def main():
    """主函数"""
    try:
        merge_fasta_files()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
