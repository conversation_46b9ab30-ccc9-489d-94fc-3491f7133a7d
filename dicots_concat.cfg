## ALIGNMENT FILE ##
alignment = concat.phy;

## BRANCHLENGTHS: linked | unlinked ##
branchlengths = linked;

## MODELS OF EVOLUTION: all | allx | mrbayes | beast | gamma | gammai | <list> ##
models = GTR, GTR+G, GTR+I+G;

# MODEL SELECTION: AIC | AICc | BIC #
model_selection = aicc;

## DATA BLOCKS: see manual for how to define ##
[data_blocks]
msa_18S_pos1 = 1-1322\3;
msa_18S_pos2 = 2-1322\3;
msa_18S_pos3 = 3-1322\3;
msa_28S_pos1 = 1323-2560\3;
msa_28S_pos2 = 1324-2560\3;
msa_28S_pos3 = 1325-2560\3;
msa_ITS_pos1 = 2561-2994\3;
msa_ITS_pos2 = 2562-2994\3;
msa_ITS_pos3 = 2563-2994\3;
msa_accD_pos1 = 2995-4091\3;
msa_accD_pos2 = 2996-4091\3;
msa_accD_pos3 = 2997-4091\3;
msa_atpA_pos1 = 4092-5603\3;
msa_atpA_pos2 = 4093-5603\3;
msa_atpA_pos3 = 4094-5603\3;
msa_atpB_pos1 = 5604-7100\3;
msa_atpB_pos2 = 5605-7100\3;
msa_atpB_pos3 = 5606-7100\3;
msa_atpE_pos1 = 7101-7499\3;
msa_atpE_pos2 = 7102-7499\3;
msa_atpE_pos3 = 7103-7499\3;
msa_atpF_pos1 = 7500-8051\3;
msa_atpF_pos2 = 7501-8051\3;
msa_atpF_pos3 = 7502-8051\3;
msa_atpH_pos1 = 8052-8297\3;
msa_atpH_pos2 = 8053-8297\3;
msa_atpH_pos3 = 8054-8297\3;
msa_atpI_pos1 = 8298-9041\3;
msa_atpI_pos2 = 8299-9041\3;
msa_atpI_pos3 = 8300-9041\3;
msa_ccsA_pos1 = 9042-10007\3;
msa_ccsA_pos2 = 9043-10007\3;
msa_ccsA_pos3 = 9044-10007\3;
msa_cemA_pos1 = 10008-10763\3;
msa_cemA_pos2 = 10009-10763\3;
msa_cemA_pos3 = 10010-10763\3;
msa_clpP_pos1 = 10764-11366\3;
msa_clpP_pos2 = 10765-11366\3;
msa_clpP_pos3 = 10766-11366\3;
msa_matK_pos1 = 11367-12941\3;
msa_matK_pos2 = 11368-12941\3;
msa_matK_pos3 = 11369-12941\3;
msa_ndhA_pos1 = 12942-14029\3;
msa_ndhA_pos2 = 12943-14029\3;
msa_ndhA_pos3 = 12944-14029\3;
msa_ndhB_pos1 = 14030-15562\3;
msa_ndhB_pos2 = 14031-15562\3;
msa_ndhB_pos3 = 14032-15562\3;
msa_ndhC_pos1 = 15563-15925\3;
msa_ndhC_pos2 = 15564-15925\3;
msa_ndhC_pos3 = 15565-15925\3;
msa_ndhD_pos1 = 15926-17482\3;
msa_ndhD_pos2 = 15927-17482\3;
msa_ndhD_pos3 = 15928-17482\3;
msa_ndhE_pos1 = 17483-17782\3;
msa_ndhE_pos2 = 17484-17782\3;
msa_ndhE_pos3 = 17485-17782\3;
msa_ndhF_pos1 = 17783-19701\3;
msa_ndhF_pos2 = 17784-19701\3;
msa_ndhF_pos3 = 17785-19701\3;
msa_ndhG_pos1 = 19702-20235\3;
msa_ndhG_pos2 = 19703-20235\3;
msa_ndhG_pos3 = 19704-20235\3;
msa_ndhH_pos1 = 20236-21191\3;
msa_ndhH_pos2 = 20237-21191\3;
msa_ndhH_pos3 = 20238-21191\3;
msa_ndhI_pos1 = 21192-21697\3;
msa_ndhI_pos2 = 21193-21697\3;
msa_ndhI_pos3 = 21194-21697\3;
msa_ndhJ_pos1 = 21698-22174\3;
msa_ndhJ_pos2 = 21699-22174\3;
msa_ndhJ_pos3 = 21700-22174\3;
msa_ndhK_pos1 = 22175-22936\3;
msa_ndhK_pos2 = 22176-22936\3;
msa_ndhK_pos3 = 22177-22936\3;
msa_petA_pos1 = 22937-23905\3;
msa_petA_pos2 = 22938-23905\3;
msa_petA_pos3 = 22939-23905\3;
msa_petB_pos1 = 23906-24553\3;
msa_petB_pos2 = 23907-24553\3;
msa_petB_pos3 = 23908-24553\3;
msa_petD_pos1 = 24554-25045\3;
msa_petD_pos2 = 24555-25045\3;
msa_petD_pos3 = 24556-25045\3;
msa_petG_pos1 = 25046-25159\3;
msa_petG_pos2 = 25047-25159\3;
msa_petG_pos3 = 25048-25159\3;
msa_petL_pos1 = 25160-25255\3;
msa_petL_pos2 = 25161-25255\3;
msa_petL_pos3 = 25162-25255\3;
msa_petN_pos1 = 25256-25345\3;
msa_petN_pos2 = 25257-25345\3;
msa_petN_pos3 = 25258-25345\3;
msa_psaA_pos1 = 25346-27268\3;
msa_psaA_pos2 = 25347-27268\3;
msa_psaA_pos3 = 25348-27268\3;
msa_psaB_pos1 = 27269-29333\3;
msa_psaB_pos2 = 27270-29333\3;
msa_psaB_pos3 = 27271-29333\3;
msa_psaC_pos1 = 29334-29579\3;
msa_psaC_pos2 = 29335-29579\3;
msa_psaC_pos3 = 29336-29579\3;
msa_psaI_pos1 = 29580-29684\3;
msa_psaI_pos2 = 29581-29684\3;
msa_psaI_pos3 = 29582-29684\3;
msa_psaJ_pos1 = 29685-29819\3;
msa_psaJ_pos2 = 29686-29819\3;
msa_psaJ_pos3 = 29687-29819\3;
msa_psbA_pos1 = 29820-30860\3;
msa_psbA_pos2 = 29821-30860\3;
msa_psbA_pos3 = 29822-30860\3;
msa_psbB_pos1 = 30861-32387\3;
msa_psbB_pos2 = 30862-32387\3;
msa_psbB_pos3 = 30863-32387\3;
msa_psbC_pos1 = 32388-33803\3;
msa_psbC_pos2 = 32389-33803\3;
msa_psbC_pos3 = 32390-33803\3;
msa_psbD_pos1 = 33804-34865\3;
msa_psbD_pos2 = 33805-34865\3;
msa_psbD_pos3 = 33806-34865\3;
msa_psbE_pos1 = 34866-35117\3;
msa_psbE_pos2 = 34867-35117\3;
msa_psbE_pos3 = 34868-35117\3;
msa_psbF_pos1 = 35118-35237\3;
msa_psbF_pos2 = 35119-35237\3;
msa_psbF_pos3 = 35120-35237\3;
msa_psbH_pos1 = 35238-35462\3;
msa_psbH_pos2 = 35239-35462\3;
msa_psbH_pos3 = 35240-35462\3;
msa_psbI_pos1 = 35463-35573\3;
msa_psbI_pos2 = 35464-35573\3;
msa_psbI_pos3 = 35465-35573\3;
msa_psbJ_pos1 = 35574-35696\3;
msa_psbJ_pos2 = 35575-35696\3;
msa_psbJ_pos3 = 35576-35696\3;
msa_psbK_pos1 = 35697-35876\3;
msa_psbK_pos2 = 35698-35876\3;
msa_psbK_pos3 = 35699-35876\3;
msa_psbL_pos1 = 35877-35993\3;
msa_psbL_pos2 = 35878-35993\3;
msa_psbL_pos3 = 35879-35993\3;
msa_psbM_pos1 = 35994-36098\3;
msa_psbM_pos2 = 35995-36098\3;
msa_psbM_pos3 = 35996-36098\3;
msa_psbN_pos1 = 36099-36230\3;
msa_psbN_pos2 = 36100-36230\3;
msa_psbN_pos3 = 36101-36230\3;
msa_psbT_pos1 = 36231-36338\3;
msa_psbT_pos2 = 36232-36338\3;
msa_psbT_pos3 = 36233-36338\3;
msa_psbZ_pos1 = 36339-36527\3;
msa_psbZ_pos2 = 36340-36527\3;
msa_psbZ_pos3 = 36341-36527\3;
msa_rbcL_pos1 = 36528-37907\3;
msa_rbcL_pos2 = 36529-37907\3;
msa_rbcL_pos3 = 36530-37907\3;
msa_rpl14_pos1 = 37908-38219\3;
msa_rpl14_pos2 = 37909-38219\3;
msa_rpl14_pos3 = 37910-38219\3;
msa_rpl16_pos1 = 38220-38665\3;
msa_rpl16_pos2 = 38221-38665\3;
msa_rpl16_pos3 = 38222-38665\3;
msa_rpl2_pos1 = 38666-39481\3;
msa_rpl2_pos2 = 38667-39481\3;
msa_rpl2_pos3 = 38668-39481\3;
msa_rpl20_pos1 = 39482-39834\3;
msa_rpl20_pos2 = 39483-39834\3;
msa_rpl20_pos3 = 39484-39834\3;
msa_rpl22_pos1 = 39835-40336\3;
msa_rpl22_pos2 = 39836-40336\3;
msa_rpl22_pos3 = 39837-40336\3;
msa_rpl23_pos1 = 40337-40807\3;
msa_rpl23_pos2 = 40338-40807\3;
msa_rpl23_pos3 = 40339-40807\3;
msa_rpl32_pos1 = 40808-40923\3;
msa_rpl32_pos2 = 40809-40923\3;
msa_rpl32_pos3 = 40810-40923\3;
msa_rpl33_pos1 = 40924-41121\3;
msa_rpl33_pos2 = 40925-41121\3;
msa_rpl33_pos3 = 40926-41121\3;
msa_rpl36_pos1 = 41122-41235\3;
msa_rpl36_pos2 = 41123-41235\3;
msa_rpl36_pos3 = 41124-41235\3;
msa_rpoA_pos1 = 41236-42180\3;
msa_rpoA_pos2 = 41237-42180\3;
msa_rpoA_pos3 = 41238-42180\3;
msa_rpoB_pos1 = 42181-45375\3;
msa_rpoB_pos2 = 42182-45375\3;
msa_rpoB_pos3 = 42183-45375\3;
msa_rpoC1_pos1 = 45376-47439\3;
msa_rpoC1_pos2 = 45377-47439\3;
msa_rpoC1_pos3 = 45378-47439\3;
msa_rpoC1exon2_pos1 = 47440-47926\3;
msa_rpoC1exon2_pos2 = 47441-47926\3;
msa_rpoC1exon2_pos3 = 47442-47926\3;
msa_rpoC2_pos1 = 47927-51973\3;
msa_rpoC2_pos2 = 47928-51973\3;
msa_rpoC2_pos3 = 47929-51973\3;
msa_rps11_pos1 = 51974-52390\3;
msa_rps11_pos2 = 51975-52390\3;
msa_rps11_pos3 = 51976-52390\3;
msa_rps12_pos1 = 52391-52648\3;
msa_rps12_pos2 = 52392-52648\3;
msa_rps12_pos3 = 52393-52648\3;
msa_rps14_pos1 = 52649-52951\3;
msa_rps14_pos2 = 52650-52951\3;
msa_rps14_pos3 = 52651-52951\3;
msa_rps15_pos1 = 52952-53207\3;
msa_rps15_pos2 = 52953-53207\3;
msa_rps15_pos3 = 52954-53207\3;
msa_rps16_pos1 = 53208-53438\3;
msa_rps16_pos2 = 53209-53438\3;
msa_rps16_pos3 = 53210-53438\3;
msa_rps18_pos1 = 53439-53736\3;
msa_rps18_pos2 = 53440-53736\3;
msa_rps18_pos3 = 53441-53736\3;
msa_rps19_pos1 = 53737-54008\3;
msa_rps19_pos2 = 53738-54008\3;
msa_rps19_pos3 = 53739-54008\3;
msa_rps2_pos1 = 54009-54716\3;
msa_rps2_pos2 = 54010-54716\3;
msa_rps2_pos3 = 54011-54716\3;
msa_rps3_pos1 = 54717-55370\3;
msa_rps3_pos2 = 54718-55370\3;
msa_rps3_pos3 = 54719-55370\3;
msa_rps4_pos1 = 55371-55967\3;
msa_rps4_pos2 = 55372-55967\3;
msa_rps4_pos3 = 55373-55967\3;
msa_rps7_pos1 = 55968-56434\3;
msa_rps7_pos2 = 55969-56434\3;
msa_rps7_pos3 = 55970-56434\3;
msa_rps8_pos1 = 56435-56839\3;
msa_rps8_pos2 = 56436-56839\3;
msa_rps8_pos3 = 56437-56839\3;
msa_rrn16_pos1 = 56840-58330\3;
msa_rrn16_pos2 = 56841-58330\3;
msa_rrn16_pos3 = 56842-58330\3;
msa_rrn23_pos1 = 58331-61132\3;
msa_rrn23_pos2 = 58332-61132\3;
msa_rrn23_pos3 = 58333-61132\3;
msa_rrn4.5_pos1 = 61133-61235\3;
msa_rrn4.5_pos2 = 61134-61235\3;
msa_rrn4.5_pos3 = 61135-61235\3;
msa_rrn5_pos1 = 61236-61356\3;
msa_rrn5_pos2 = 61237-61356\3;
msa_rrn5_pos3 = 61238-61356\3;
msa_ycf3_pos1 = 61357-61860\3;
msa_ycf3_pos2 = 61358-61860\3;
msa_ycf3_pos3 = 61359-61860\3;
msa_ycf4_pos1 = 61861-62415\3;
msa_ycf4_pos2 = 61862-62415\3;
msa_ycf4_pos3 = 61863-62415\3;

## SCHEMES, search: all | user | greedy | rcluster | rclusterf | kmeans ##
[schemes]
search = greedy;