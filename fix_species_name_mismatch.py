#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复物种名称不匹配问题
从实际的FASTA文件中提取物种名称，创建匹配的骨架树
"""

import os
import re
from Bio import SeqIO

def extract_species_from_fasta_files():
    """
    从当前目录的FASTA文件中提取所有物种名称
    """
    print("从FASTA文件中提取物种名称...")
    
    all_species = set()
    fasta_files = [f for f in os.listdir('.') if f.endswith('.fasta')]
    
    print(f"找到 {len(fasta_files)} 个FASTA文件")
    
    for fasta_file in fasta_files[:5]:  # 只检查前5个文件作为示例
        print(f"  检查文件: {fasta_file}")
        try:
            with open(fasta_file, 'r') as f:
                for record in SeqIO.parse(f, 'fasta'):
                    # 提取物种名称的不同模式
                    species_id = record.id
                    
                    # 模式1: 直接的 Genus_species 格式
                    match1 = re.search(r'\b([A-Z][a-z]+_[a-z]+)\b', species_id)
                    if match1:
                        all_species.add(match1.group(1))
                    
                    # 模式2: 包含下划线的复杂格式
                    match2 = re.search(r'\b([A-Z][a-z]+_[a-z]+_[a-z]+)\b', species_id)
                    if match2:
                        all_species.add(match2.group(1))
                    
                    # 只检查前几个序列
                    if len(all_species) > 20:
                        break
                        
        except Exception as e:
            print(f"    读取 {fasta_file} 时出错: {str(e)}")
            continue
    
    return sorted(list(all_species))

def check_species_in_classification_files(species_list):
    """
    检查物种在分类文件中的分布
    """
    print(f"\n检查 {len(species_list)} 个物种在分类文件中的分布...")
    
    classification_files = [
        'superrosids.txt',
        'ANA.txt', 
        'dicots.txt',
        'monocot.txt',
        'superasterids.txt'
    ]
    
    classification_species = {}
    
    for class_file in classification_files:
        if os.path.exists(class_file):
            print(f"  检查 {class_file}...")
            try:
                with open(class_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取该分类中的物种
                class_species = set()
                for species in species_list:
                    if species in content:
                        class_species.add(species)
                
                classification_species[class_file.replace('.txt', '')] = class_species
                print(f"    找到 {len(class_species)} 个匹配物种")
                
            except Exception as e:
                print(f"    读取 {class_file} 时出错: {str(e)}")
        else:
            print(f"  ⚠️ {class_file} 不存在")
    
    return classification_species

def create_matching_skeleton_trees(classification_species):
    """
    为每个分类创建匹配的骨架树
    """
    print(f"\n创建匹配的骨架树...")
    
    for classification, species_set in classification_species.items():
        if len(species_set) == 0:
            print(f"  ⚠️ {classification}: 没有找到匹配的物种")
            continue
            
        species_list = sorted(list(species_set))
        print(f"  {classification}: {len(species_list)} 个物种")
        
        # 创建简单的星形树
        if len(species_list) == 1:
            tree_content = f"{species_list[0]};"
        else:
            tree_content = f"({','.join(species_list)});"
        
        # 保存树文件
        output_file = f"{classification}_MATCHED.tre"
        with open(output_file, 'w') as f:
            f.write(tree_content)
        
        print(f"    ✅ 创建: {output_file}")
        
        # 验证树文件
        open_count = tree_content.count('(')
        close_count = tree_content.count(')')
        print(f"    验证: {open_count} 开括号, {close_count} 关括号")

def analyze_problematic_species():
    """
    分析报错中提到的问题物种
    """
    print(f"\n分析报错中的问题物种...")
    
    problematic_species = [
        "Breynia_coronata",
        "Passiflora_capparidifolia", 
        "Passiflora_picturata",
        "Oxalis_rhombeo",
        "Oxalis_pes",
        "Quercus_imbricaria",
        "Paliurus_spina",
        "Pomaderris_notata",
        "Cotoneaster_orbicularis",
        "Potentilla_pedata"
    ]
    
    print(f"问题物种示例: {problematic_species[:5]}")
    
    # 检查这些物种是否在FASTA文件中
    fasta_files = [f for f in os.listdir('.') if f.endswith('.fasta')]
    found_species = set()
    
    for fasta_file in fasta_files[:3]:  # 只检查前3个文件
        try:
            with open(fasta_file, 'r') as f:
                content = f.read()
                for species in problematic_species:
                    if species in content:
                        found_species.add(species)
        except:
            continue
    
    print(f"在FASTA文件中找到的问题物种: {len(found_species)} 个")
    if found_species:
        print(f"  找到的物种: {list(found_species)[:3]}...")
    
    # 检查是否是名称截断问题
    print(f"\n检查是否是名称截断问题...")
    truncated_patterns = []
    for species in problematic_species:
        # 检查是否有截断的版本
        genus = species.split('_')[0]
        species_part = species.split('_')[1]
        
        # 可能的截断模式
        patterns = [
            f"{genus}_{species_part[:5]}",  # 截断到5个字符
            f"{genus}_{species_part[:6]}",  # 截断到6个字符
            f"{genus}_{species_part[:7]}",  # 截断到7个字符
        ]
        truncated_patterns.extend(patterns)
    
    print(f"可能的截断模式示例: {truncated_patterns[:5]}")
    
    return problematic_species, truncated_patterns

def create_safe_skeleton_trees():
    """
    创建安全的骨架树，避免物种名称不匹配
    """
    print(f"\n创建安全的骨架树...")
    
    # 为每个分类创建最小的测试树
    classifications = ['superrosids', 'ANA', 'dicots', 'monocot', 'superasterids']
    
    for classification in classifications:
        # 创建只包含几个通用物种的测试树
        test_species = [
            f"{classification}_species_1",
            f"{classification}_species_2", 
            f"{classification}_species_3"
        ]
        
        tree_content = f"({','.join(test_species)});"
        output_file = f"{classification}_TEST_SAFE.tre"
        
        with open(output_file, 'w') as f:
            f.write(tree_content)
        
        print(f"  ✅ 创建测试树: {output_file}")

def main():
    """主函数"""
    print("🔧 修复物种名称不匹配问题")
    print("目标: 创建与实际序列文件匹配的骨架树")
    print("=" * 80)
    
    # 1. 从FASTA文件中提取物种名称
    fasta_species = extract_species_from_fasta_files()
    
    if fasta_species:
        print(f"\n从FASTA文件中提取到的物种示例:")
        for i, species in enumerate(fasta_species[:10]):
            print(f"  {i+1}. {species}")
        
        # 2. 检查物种在分类文件中的分布
        classification_species = check_species_in_classification_files(fasta_species)
        
        # 3. 创建匹配的骨架树
        if classification_species:
            create_matching_skeleton_trees(classification_species)
    else:
        print("⚠️ 未能从FASTA文件中提取到物种名称")
    
    # 4. 分析问题物种
    analyze_problematic_species()
    
    # 5. 创建安全的测试树
    create_safe_skeleton_trees()
    
    print(f"\n💡 建议:")
    print(f"1. 首先使用 *_MATCHED.tre 文件（如果创建成功）")
    print(f"2. 如果失败，使用 *_TEST_SAFE.tre 文件进行测试")
    print(f"3. 检查实际的序列文件格式和物种名称")
    print(f"4. 考虑不使用约束树，让IQ-TREE自由构建")
    
    print(f"\n📝 测试命令:")
    print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_TEST_SAFE.tre -m MFP")

if __name__ == "__main__":
    main()
