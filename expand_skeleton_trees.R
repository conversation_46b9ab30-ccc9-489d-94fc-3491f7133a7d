# 将骨架树扩展为包含物种的系统发育树
# 将科名替换为该科下的所有物种名称

library(ape)

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

cat("将骨架树扩展为包含物种的系统发育树\n")
cat(paste(rep("=", 60), collapse = ""), "\n\n")

# 分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

# 函数：将科名替换为该科的物种列表
replace_family_with_species <- function(tree_string, family_species_map) {
  modified_string <- tree_string

  for (family in names(family_species_map)) {
    species_list <- family_species_map[[family]]

    if (length(species_list) > 0) {
      # 清理物种名称，确保格式正确
      cleaned_species <- gsub("[^A-Za-z0-9_-]", "_", species_list)

      # 将物种名称用逗号连接，并用括号包围
      species_string <- paste("(", paste(cleaned_species, collapse = ","), ")", sep = "")

      # 替换科名为物种列表
      modified_string <- gsub(family, species_string, modified_string, fixed = TRUE)
    }
  }

  return(modified_string)
}

# 处理每个分类
for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  subtree_file <- paste0(class_name, "_subtree.tre")
  
  cat("处理", class_name, "分类:\n")
  
  # 检查文件是否存在
  if (!file.exists(class_file) || !file.exists(subtree_file)) {
    cat("  ✗ 缺少必要文件\n\n")
    next
  }
  
  # 读取分类数据
  class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
  
  # 创建科-物种映射
  family_species_map <- list()
  for (i in 1:nrow(class_data)) {
    family <- class_data$wcvp_family[i]
    species <- class_data$wcvp_searchedName[i]
    
    if (!is.na(family) && !is.na(species)) {
      if (family %in% names(family_species_map)) {
        family_species_map[[family]] <- c(family_species_map[[family]], species)
      } else {
        family_species_map[[family]] <- species
      }
    }
  }
  
  cat("  - 包含", length(family_species_map), "个科\n")
  cat("  - 总计", sum(sapply(family_species_map, length)), "个物种\n")
  
  # 读取骨架树
  skeleton_tree <- read.tree(subtree_file)
  
  # 将树转换为字符串
  tree_string <- write.tree(skeleton_tree)
  
  cat("  - 原始骨架树:", nchar(tree_string), "个字符\n")
  
  # 替换科名为物种列表
  expanded_tree_string <- replace_family_with_species(tree_string, family_species_map)
  
  cat("  - 扩展后树:", nchar(expanded_tree_string), "个字符\n")
  
  # 保存扩展后的树
  output_file <- paste0(class_name, "_species_tree.tre")
  writeLines(expanded_tree_string, output_file)
  
  cat("  ✓ 扩展树已保存为:", output_file, "\n")
  
  # 显示一些统计信息
  families_in_tree <- skeleton_tree$tip.label
  families_with_species <- intersect(families_in_tree, names(family_species_map))
  
  cat("  - 骨架树中的科:", length(families_in_tree), "个\n")
  cat("  - 有物种数据的科:", length(families_with_species), "个\n")
  
  if (length(families_with_species) < length(families_in_tree)) {
    missing_families <- setdiff(families_in_tree, names(family_species_map))
    cat("  ⚠ 缺少物种数据的科:", paste(missing_families, collapse = ", "), "\n")
  }
  
  # 显示每个科的物种数量
  cat("  - 各科物种数量:\n")
  for (family in families_with_species[1:min(5, length(families_with_species))]) {
    species_count <- length(family_species_map[[family]])
    cat("    ", family, ":", species_count, "个物种\n")
  }
  if (length(families_with_species) > 5) {
    cat("    ... (还有", length(families_with_species) - 5, "个科)\n")
  }
  
  cat("\n")
}

cat("所有分类的骨架树扩展完成！\n")
cat("\n使用说明:\n")
cat("- *_species_tree.tre 文件包含了完整的物种级别系统发育树\n")
cat("- 每个科的位置被替换为该科下的所有物种\n")
cat("- 物种名称用逗号分隔，并用括号包围\n")
cat("- 可以用于物种级别的系统发育分析\n")
