#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并当前路径和filtered_fasta250629文件夹中名称相同的FASTA文件
输出到250629文件夹，保证不改变原序列
"""

import os
from Bio import SeqIO

def merge_fasta_files():
    """
    主要功能：合并当前路径和filtered_fasta250629文件夹中名称相同的FASTA文件
    """
    # 定义路径
    current_dir = "."  # 当前路径
    filtered_dir = "filtered_fasta250629"  # 过滤后的文件夹
    output_dir = "250629"  # 输出文件夹
    
    print("🔄 FASTA文件合并工具")
    print("=" * 70)
    print(f"📁 当前路径: {os.path.abspath(current_dir)}")
    print(f"📁 过滤文件夹: {os.path.abspath(filtered_dir)}")
    print(f"📁 输出文件夹: {os.path.abspath(output_dir)}")
    print("=" * 70)
    
    # 检查filtered_fasta250629文件夹是否存在
    if not os.path.exists(filtered_dir):
        print(f"❌ 错误: {filtered_dir} 文件夹不存在！")
        print("请确保filtered_fasta250629文件夹在当前目录下")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"✅ 输出目录已创建: {output_dir}")
    
    # 获取当前路径下的FASTA文件（排除子文件夹中的文件）
    current_files = []
    for f in os.listdir(current_dir):
        if f.endswith('.fasta') and os.path.isfile(os.path.join(current_dir, f)):
            current_files.append(f)
    
    # 获取filtered_fasta250629文件夹中的FASTA文件
    filtered_files = []
    for f in os.listdir(filtered_dir):
        if f.endswith('.fasta'):
            filtered_files.append(f)
    
    print(f"\n📊 文件统计:")
    print(f"   当前路径中的FASTA文件: {len(current_files)} 个")
    print(f"   filtered_fasta250629中的FASTA文件: {len(filtered_files)} 个")
    
    # 找到名称相同的文件
    current_set = set(current_files)
    filtered_set = set(filtered_files)
    common_files = current_set.intersection(filtered_set)
    
    print(f"   名称相同的文件: {len(common_files)} 个")
    
    if len(common_files) == 0:
        print("\n⚠️ 警告: 没有找到名称相同的文件！")
        print("请检查文件名是否匹配")
        return False
    
    # 显示将要合并的文件列表
    print(f"\n📋 将要合并的文件列表:")
    for i, filename in enumerate(sorted(common_files), 1):
        print(f"   {i:2d}. {filename}")
    
    # 统计变量
    successful_merges = 0
    failed_merges = 0
    total_sequences_merged = 0
    
    print(f"\n🔄 开始合并文件...")
    print("-" * 70)
    
    # 遍历每个相同名称的文件进行合并
    for i, filename in enumerate(sorted(common_files), 1):
        try:
            print(f"\n[{i}/{len(common_files)}] 处理文件: {filename}")
            
            # 构建文件路径
            current_file_path = os.path.join(current_dir, filename)
            filtered_file_path = os.path.join(filtered_dir, filename)
            output_file_path = os.path.join(output_dir, filename)
            
            # 读取并统计序列
            current_sequences = list(SeqIO.parse(current_file_path, "fasta"))
            filtered_sequences = list(SeqIO.parse(filtered_file_path, "fasta"))
            
            print(f"   📄 当前路径序列数: {len(current_sequences)}")
            print(f"   📄 过滤文件夹序列数: {len(filtered_sequences)}")
            
            # 合并序列到输出文件
            with open(output_file_path, "w") as output_handle:
                # 先写入当前路径中的序列
                for record in current_sequences:
                    SeqIO.write(record, output_handle, "fasta")
                
                # 再写入filtered_fasta250629中的序列
                for record in filtered_sequences:
                    SeqIO.write(record, output_handle, "fasta")
            
            total_sequences_in_output = len(current_sequences) + len(filtered_sequences)
            total_sequences_merged += total_sequences_in_output
            
            print(f"   ✅ 合并完成! 输出序列总数: {total_sequences_in_output}")
            successful_merges += 1
            
        except Exception as e:
            print(f"   ❌ 合并失败: {str(e)}")
            failed_merges += 1
    
    # 输出最终统计结果
    print(f"\n" + "=" * 70)
    print("🎉 合并完成！最终统计结果:")
    print("=" * 70)
    print(f"✅ 成功合并的文件数: {successful_merges}")
    print(f"❌ 合并失败的文件数: {failed_merges}")
    print(f"🧬 合并的序列总数: {total_sequences_merged}")
    print(f"📁 输出文件夹: {os.path.abspath(output_dir)}")
    
    if successful_merges > 0:
        # 验证输出文件
        output_files = [f for f in os.listdir(output_dir) if f.endswith('.fasta')]
        print(f"📊 输出文件夹中的文件数: {len(output_files)}")
        
        # 显示输出文件列表（最多显示10个）
        if len(output_files) <= 10:
            print(f"\n📋 输出的文件列表:")
            for f in sorted(output_files):
                print(f"   • {f}")
        else:
            print(f"\n📋 输出的文件列表（前10个）:")
            for f in sorted(output_files)[:10]:
                print(f"   • {f}")
            print(f"   ... 还有 {len(output_files) - 10} 个文件")
    
    # 显示未匹配的文件信息
    only_in_current = current_set - filtered_set
    only_in_filtered = filtered_set - current_set
    
    if only_in_current:
        print(f"\n⚠️ 只在当前路径中存在的文件 ({len(only_in_current)} 个):")
        for f in sorted(list(only_in_current)[:5]):  # 最多显示5个
            print(f"   • {f}")
        if len(only_in_current) > 5:
            print(f"   ... 还有 {len(only_in_current) - 5} 个文件")
    
    if only_in_filtered:
        print(f"\n⚠️ 只在filtered_fasta250629中存在的文件 ({len(only_in_filtered)} 个):")
        for f in sorted(list(only_in_filtered)[:5]):  # 最多显示5个
            print(f"   • {f}")
        if len(only_in_filtered) > 5:
            print(f"   ... 还有 {len(only_in_filtered) - 5} 个文件")
    
    return successful_merges > 0

def main():
    """主函数"""
    try:
        print("🧬 FASTA文件合并工具启动")
        print("功能: 合并当前路径和filtered_fasta250629文件夹中名称相同的FASTA文件")
        print("输出: 250629文件夹\n")
        
        success = merge_fasta_files()
        
        if success:
            print(f"\n🎉 程序执行成功！")
            print(f"💡 提示: 合并后的文件已保存在 250629 文件夹中")
        else:
            print(f"\n⚠️ 程序执行完成，但可能存在问题")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
