#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有5个分类创建正确的骨架树
"""

import re
import os

def extract_species_from_tree(tree_string):
    """从树字符串中提取所有物种名称"""
    species_pattern = r'\b([A-Za-z][A-Za-z0-9_-]*_[A-Za-z0-9_-]*)\b'
    all_species = re.findall(species_pattern, tree_string)
    unique_species = sorted(list(set(all_species)))
    return unique_species

def create_taxonomic_skeleton_tree(species_list, classification_name):
    """基于分类学关系创建骨架树"""
    
    # 为每个分类定义主要分组
    classification_groups = {
        'dicots': {
            'Ranunculales': ['Ranunculus', 'Thalictrum', 'Anemone', 'Aquilegia', 'Clematis', 'Aconitum', 'Actaea', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Epimedium', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ium', '<PERSON>lau<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ype<PERSON><PERSON>', '<PERSON><PERSON>cap<PERSON>', '<PERSON><PERSON><PERSON>st<PERSON>', 'Eschscholzia', 'Lamprocapnos', 'Anemonastrum', 'Anemonoides', 'Anemonella', 'Eriocapitella', 'Hepatica', 'Pulsatilla', 'Trollius', 'Caltha', 'Coptis', 'Delphinium', 'Eranthis', 'Helleborus', 'Nigella', 'Xanthorhiza', 'Hydrastis', 'Jeffersonia', 'Podophyllum', 'Caulophyllum', 'Nandina', 'Gymnospermium', 'Plagiorhegma', 'Asteropyrum', 'Beesia', 'Callianthemum', 'Enemion', 'Glaucidium', 'Isopyrum', 'Knowltonia', 'Anemonopsis', 'Halerpestes'],
            'Proteales': ['Protea', 'Grevillea', 'Banksia', 'Hakea', 'Macadamia', 'Leucadendron', 'Telopea', 'Lomatia', 'Persoonia', 'Conospermum', 'Lambertia', 'Alloxylon', 'Adenanthos', 'Aulax', 'Bellendena', 'Brabejum', 'Buckinghamia', 'Cardwellia', 'Carnarvonia', 'Catalepidia', 'Cenarrhenes', 'Darlingia', 'Floydia', 'Helicia', 'Hicksbeachia', 'Hollandaea', 'Knightia', 'Lasjia', 'Musgravea', 'Neorites', 'Opisthiolepis', 'Petrophile', 'Placospermum', 'Sphalmium', 'Stenocarpus', 'Stirlingia', 'Strangea', 'Symphionema', 'Triunia', 'Virotia', 'Agastachys', 'Athertonia', 'Austromuellera', 'Platanus', 'Nelumbo'],
            'Buxales': ['Buxus', 'Sarcococca', 'Pachysandra'],
            'Trochodendrales': ['Trochodendron', 'Tetracentron'],
            'Gunnerales': ['Gunnera'],
            'Dilleniaceae': ['Hibbertia', 'Dillenia'],
            'Menispermaceae': ['Sinomenium', 'Menispermum'],
            'Lardizabalaceae': ['Stauntonia', 'Akebia', 'Decaisnea'],
            'Sabiaceae': ['Meliosma'],
            'Eupteleaceae': ['Euptelea']
        },
        'monocot': {
            'Poales': ['Poaceae', 'Cyperaceae', 'Juncaceae', 'Bromeliaceae', 'Typhaceae', 'Eriocaulaceae', 'Xyridaceae', 'Mayacaceae', 'Rapateaceae', 'Centrolepidaceae', 'Restionaceae', 'Anarthriaceae', 'Flagellariaceae', 'Joinvilleaceae'],
            'Asparagales': ['Orchidaceae', 'Asparagaceae', 'Amaryllidaceae', 'Iridaceae', 'Xanthorrhoeaceae', 'Alliaceae', 'Agapanthaceae', 'Amaryllidaceae', 'Asphodelaceae', 'Hemerocallidaceae', 'Hyacinthaceae', 'Ruscaceae', 'Themidaceae'],
            'Liliales': ['Liliaceae', 'Colchicaceae', 'Smilacaceae', 'Philesiaceae', 'Rhipogonaceae', 'Campynemataceae', 'Corsiaceae', 'Luzuriagaceae', 'Petermanniaceae', 'Alstroemeriaceae', 'Melanthiaceae'],
            'Dioscoreales': ['Dioscoreaceae', 'Burmanniaceae', 'Nartheciaceae', 'Taccaceae'],
            'Pandanales': ['Pandanaceae', 'Velloziaceae', 'Triuridaceae', 'Cyclanthaceae', 'Stemonaceae'],
            'Petrosaviales': ['Petrosaviaceae'],
            'Alismatales': ['Alismataceae', 'Araceae', 'Hydrocharitaceae', 'Butomaceae', 'Cymodoceaceae', 'Juncaginaceae', 'Potamogetonaceae', 'Ruppiaceae', 'Scheuchzeriaceae', 'Tofieldiaceae', 'Zosteraceae', 'Posidoniaceae', 'Aponogetonaceae'],
            'Arecales': ['Arecaceae', 'Dasypogonaceae'],
            'Commelinales': ['Commelinaceae', 'Haemodoraceae', 'Hanguanaceae', 'Philydraceae', 'Pontederiaceae'],
            'Zingiberales': ['Zingiberaceae', 'Costaceae', 'Cannaceae', 'Marantaceae', 'Lowiaceae', 'Strelitziaceae', 'Heliconiaceae', 'Musaceae'],
            'Acorales': ['Acoraceae'],
            'Alismatales': ['Alismataceae', 'Araceae', 'Hydrocharitaceae']
        },
        'superrosids': {
            'Fabales': ['Fabaceae', 'Quillajaceae', 'Surianaceae', 'Polygalaceae'],
            'Rosales': ['Rosaceae', 'Rhamnaceae', 'Ulmaceae', 'Cannabaceae', 'Moraceae', 'Urticaceae', 'Elaeagnaceae', 'Barbeyaceae', 'Dirachmaceae'],
            'Cucurbitales': ['Cucurbitaceae', 'Begoniaceae', 'Datiscaceae', 'Tetramelaceae', 'Anisophylleaceae', 'Coriariaceae', 'Corynocarpaceae'],
            'Fagales': ['Fagaceae', 'Betulaceae', 'Casuarinaceae', 'Juglandaceae', 'Myricaceae', 'Nothofagaceae', 'Rhoipteleaceae', 'Ticodendraceae'],
            'Malpighiales': ['Malpighiaceae', 'Euphorbiaceae', 'Phyllanthaceae', 'Picrodendraceae', 'Putranjivaceae', 'Centroplacaceae', 'Caryocaraceae', 'Chrysobalanaceae', 'Dichapetalaceae', 'Trigoniaceae', 'Clusiaceae', 'Calophyllaceae', 'Podostemaceae', 'Hypericaceae', 'Elatinaceae', 'Malpighiaceae', 'Balanopaceae', 'Goupiaceae', 'Irvingiaceae', 'Ixonanthaceae', 'Lacistemataceae', 'Linaceae', 'Lophopyxidaceae', 'Ochnaceae', 'Pandaceae', 'Passifloraceae', 'Peraceae', 'Rafflesiaceae', 'Salicaceae', 'Violaceae', 'Achariaceae', 'Humiriaceae', 'Erythroxylaceae'],
            'Oxalidales': ['Oxalidaceae', 'Connaraceae', 'Brunelliaceae', 'Cephalotaceae', 'Cunoniaceae', 'Elaeocarpaceae', 'Tremandraceae'],
            'Celastrales': ['Celastraceae', 'Lepidobotryaceae', 'Parnassiaceae'],
            'Zygophyllales': ['Zygophyllaceae', 'Krameriaceae'],
            'Vitales': ['Vitaceae'],
            'Geraniales': ['Geraniaceae', 'Francoaceae', 'Ledocarpaceae', 'Melianthaceae', 'Vivianiaceae'],
            'Myrtales': ['Myrtaceae', 'Combretaceae', 'Lythraceae', 'Melastomataceae', 'Onagraceae', 'Penaeaceae', 'Vochysiaceae', 'Alzateaceae', 'Crypteroniaceae'],
            'Crossosomatales': ['Crossosomataceae', 'Stachyuraceae', 'Staphyleaceae', 'Guamatelaceae', 'Strasburgeriaceae', 'Aphloiaceae', 'Geissolomataceae', 'Ixerbaceae', 'Picramniaceae', 'Tepuianthaceae'],
            'Picramniales': ['Picramniaceae'],
            'Huerteales': ['Gerrardinaceae', 'Petenaeaceae', 'Tapisciaceae', 'Dipentodontaceae'],
            'Brassicales': ['Brassicaceae', 'Capparaceae', 'Cleomaceae', 'Emblingiaceae', 'Gyrostemonaceae', 'Koeberliniaceae', 'Limnanthaceae', 'Moringaceae', 'Pentadiplandraceae', 'Resedaceae', 'Salvadoraceae', 'Setchellanthaceae', 'Tovariaceae', 'Tropaeolaceae', 'Akaniaceae', 'Bataceae', 'Caricaceae'],
            'Malvales': ['Malvaceae', 'Bixaceae', 'Cistaceae', 'Cytinaceae', 'Dipterocarpaceae', 'Muntingiaceae', 'Neuradaceae', 'Sarcolaenaceae', 'Sphaerosepalaceae', 'Thymelaeaceae'],
            'Sapindales': ['Sapindaceae', 'Rutaceae', 'Meliaceae', 'Mahogany', 'Anacardiaceae', 'Burseraceae', 'Kirkiaceae', 'Nitrariaceae', 'Sapindaceae', 'Simaroubaceae']
        },
        'superasterids': {
            'Asterales': ['Asteraceae', 'Campanulaceae', 'Menyanthaceae', 'Argophyllaceae', 'Alseuosmiaceae', 'Phellinaceae', 'Rousseaceae', 'Stylidiaceae', 'Goodeniaceae', 'Calyceraceae'],
            'Apiales': ['Apiaceae', 'Araliaceae', 'Pittosporaceae', 'Torricelliaceae', 'Griseliniaceae', 'Pennantiaceae', 'Myodocarpaceae'],
            'Dipsacales': ['Caprifoliaceae', 'Adoxaceae', 'Viburnaceae', 'Sambucaceae', 'Linnaeaceae', 'Diervillaceae', 'Dipsacaceae', 'Morinaceae', 'Valerianaceae'],
            'Aquifoliales': ['Aquifoliaceae', 'Helwingiaceae', 'Phyllonomaceae', 'Stemonuraceae', 'Cardiopteridaceae'],
            'Bruniales': ['Bruniaceae', 'Columelliaceae'],
            'Paracryphiales': ['Paracryphiaceae'],
            'Escalloniales': ['Escalloniaceae'],
            'Cornales': ['Cornaceae', 'Curtisiaceae', 'Grubbiaceae', 'Hydrangeaceae', 'Hydrostachyaceae', 'Loasaceae', 'Nyssaceae'],
            'Ericales': ['Ericaceae', 'Balsaminaceae', 'Marcgraviaceae', 'Tetrameristaceae', 'Fouquieriaceae', 'Polemoniaceae', 'Lecythidaceae', 'Pentaphylacaceae', 'Sladeniaceae', 'Symplocaceae', 'Diapensiaceae', 'Styracaceae', 'Sarraceniaceae', 'Roridulaceae', 'Actinidiaceae', 'Clethraceae', 'Cyrillaceae', 'Ebenaceae', 'Mitrastemonaceae', 'Monotropaceae', 'Primulaceae', 'Sapotaceae', 'Theaceae'],
            'Garryales': ['Garryaceae', 'Eucommiaceae', 'Aucubaceae'],
            'Gentianales': ['Gentianaceae', 'Loganiaceae', 'Gelsemiaceae', 'Apocynaceae', 'Rubiaceae'],
            'Lamiales': ['Lamiaceae', 'Verbenaceae', 'Oleaceae', 'Plantaginaceae', 'Scrophulariaceae', 'Stilbaceae', 'Linderniaceae', 'Bonnetiaceae', 'Tetrachondraceae', 'Calceolariaceae', 'Gesneriaceae', 'Paulowniaceae', 'Phrymaceae', 'Mazaceae', 'Rehmanniaceae', 'Schlegeliaceae', 'Thomandersiaceae', 'Verbenaceae', 'Acanthaceae', 'Bignoniaceae', 'Lentibulariaceae', 'Martyniaceae', 'Pedaliaceae'],
            'Solanales': ['Solanaceae', 'Convolvulaceae', 'Hydroleaceae', 'Montiniaceae', 'Sphenocleaceae'],
            'Boraginales': ['Boraginaceae', 'Hydrophyllaceae', 'Namaceae', 'Heliotropiaceae', 'Ehretiaceae', 'Cordiaceae', 'Wellstediaceae'],
            'Vahliales': ['Vahliaceae'],
            'Icacinales': ['Icacinaceae', 'Oncothecaceae', 'Metteniusaceae']
        }
    }
    
    # 如果分类不在预定义中，创建基于属的简单分组
    if classification_name not in classification_groups:
        genus_groups = {}
        for species in species_list:
            genus = species.split('_')[0]
            if genus not in genus_groups:
                genus_groups[genus] = []
            genus_groups[genus].append(species)
        
        # 创建简单的分层结构
        group_parts = []
        for genus, species_in_genus in sorted(genus_groups.items()):
            if len(species_in_genus) == 1:
                group_parts.append(species_in_genus[0])
            else:
                group_parts.append(f"({','.join(sorted(species_in_genus))})")
        
        # 创建平衡的二分树
        if len(group_parts) <= 4:
            return f"({','.join(group_parts)});"
        else:
            mid = len(group_parts) // 2
            left_part = f"({','.join(group_parts[:mid])})"
            right_part = f"({','.join(group_parts[mid:])})"
            return f"({left_part},{right_part});"
    
    # 使用预定义的分组
    major_groups = classification_groups[classification_name]
    
    # 将物种分配到主要分组
    grouped_species = {}
    unassigned_species = []
    
    for species in species_list:
        genus = species.split('_')[0]
        assigned = False
        
        for group_name, genera in major_groups.items():
            if genus in genera:
                if group_name not in grouped_species:
                    grouped_species[group_name] = []
                grouped_species[group_name].append(species)
                assigned = True
                break
        
        if not assigned:
            unassigned_species.append(species)
    
    print(f"分组结果:")
    for group_name, species_in_group in grouped_species.items():
        print(f"  {group_name}: {len(species_in_group)} 个物种")
    
    if unassigned_species:
        print(f"  未分组: {len(unassigned_species)} 个物种")
    
    # 创建分层的树结构
    group_parts = []
    
    for group_name in sorted(grouped_species.keys()):
        species_in_group = sorted(grouped_species[group_name])
        if len(species_in_group) == 1:
            group_parts.append(species_in_group[0])
        else:
            group_parts.append(f"({','.join(species_in_group)})")
    
    # 添加未分组的物种
    if unassigned_species:
        if len(unassigned_species) == 1:
            group_parts.append(unassigned_species[0])
        else:
            group_parts.append(f"({','.join(sorted(unassigned_species))})")
    
    # 创建最终的树结构
    if len(group_parts) <= 2:
        tree = f"({','.join(group_parts)});"
    elif len(group_parts) <= 4:
        tree = f"({','.join(group_parts)});"
    else:
        # 创建平衡的分层结构
        mid = len(group_parts) // 2
        left_part = f"({','.join(group_parts[:mid])})"
        right_part = f"({','.join(group_parts[mid:])})"
        tree = f"({left_part},{right_part});"
    
    return tree

def process_classification(classification_name):
    """处理单个分类"""
    input_file = f"{classification_name}_species_tree.tre"
    output_file = f"{classification_name}_taxonomic_skeleton_tree.tre"
    
    print(f"\n🌳 处理 {classification_name} 分类")
    print("=" * 50)
    
    if not os.path.exists(input_file):
        print(f"❌ 文件 {input_file} 不存在，跳过")
        return False
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 提取物种
        species_list = extract_species_from_tree(original_tree)
        print(f"提取到 {len(species_list)} 个物种")
        
        # 创建分类学骨架树
        skeleton_tree = create_taxonomic_skeleton_tree(species_list, classification_name)
        
        # 保存骨架树
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(skeleton_tree)
        
        # 验证
        open_count = skeleton_tree.count('(')
        close_count = skeleton_tree.count(')')
        
        print(f"✅ 创建: {output_file}")
        print(f"   长度: {len(skeleton_tree)} 字符")
        print(f"   括号: {open_count} 开, {close_count} 关")
        print(f"   平衡: {'是' if open_count == close_count else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理 {classification_name} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    classifications = ['dicots', 'monocot', 'superrosids', 'superasterids']
    
    print("🌳 批量创建所有分类的骨架树")
    print("目标: 为每个分类创建兼容IQ-TREE的分类学骨架树")
    print("=" * 70)
    
    success_count = 0
    
    for classification in classifications:
        if process_classification(classification):
            success_count += 1
    
    print(f"\n📊 处理总结:")
    print(f"成功处理: {success_count}/{len(classifications)} 个分类")
    
    if success_count > 0:
        print(f"\n📝 IQ-TREE使用示例:")
        for classification in classifications:
            output_file = f"{classification}_taxonomic_skeleton_tree.tre"
            if os.path.exists(output_file):
                print(f"iqtree2 -s concat.phy -p {classification}_concat.txt -t {output_file} -m MFP -bb 1000")
    
    print(f"\n🎉 所有骨架树创建完成!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
