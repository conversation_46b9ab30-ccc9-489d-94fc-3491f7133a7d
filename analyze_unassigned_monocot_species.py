#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析monocot中未分组的物种，改进分类
"""

import re
import os
from collections import Counter

def extract_species_from_tree(tree_file):
    """从树文件中提取所有物种"""
    with open(tree_file, 'r', encoding='utf-8') as f:
        tree_string = f.read().strip()
    
    species_pattern = r'\b([A-Z][a-z]+_[a-z-]+)\b'
    species_matches = re.findall(species_pattern, tree_string)
    unique_species = sorted(list(set(species_matches)))
    
    return unique_species

def analyze_unassigned_genera(species_list):
    """分析未分组物种的属分布"""
    
    # 扩展的单子叶植物分类系统
    monocot_orders_extended = {
        'Acorales': ['Acorus'],
        'Alismatales': [
            # 泽泻科
            'Alisma', 'Aponogeton', 'Butomus', 'Hydrocharis', 'Potamogeton', '<PERSON>gittaria', 
            '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>op<PERSON><PERSON>', '<PERSON><PERSON>', 
            '<PERSON><PERSON><PERSON>', 'Sche<PERSON>zer<PERSON>', 'Triglochin',
            # 天南星科
            'Alocasia', 'Amorphophallus', 'Anthurium', 'Arisaema', 'Arisarum', 'Arum', 
            'Caladium', 'Calla', 'Colocasia', 'Cryptocoryne', 'Dieffenbachia', 'Dracunculus', 
            'Epipremnum', 'Homalomena', 'Monstera', 'Philodendron', 'Pistia', 'Spathiphyllum', 
            'Symplocarpus', 'Xanthosoma', 'Zantedeschia'
        ],
        'Asparagales': [
            # 天门冬科、石蒜科、鸢尾科等
            'Agapanthus', 'Agave', 'Albuca', 'Allium', 'Aloe', 'Amaryllis', 'Anthericum', 
            'Asparagus', 'Asphodeline', 'Asphodelus', 'Aspidistra', 'Bellevalia', 'Bowiea', 
            'Brodiaea', 'Bulbine', 'Camassia', 'Chlorophytum', 'Colchicum', 'Convallaria', 
            'Cordyline', 'Crinum', 'Crocus', 'Curculigo', 'Cyrtanthus', 'Dianella', 'Dipcadi', 
            'Dracaena', 'Drimia', 'Drimiopsis', 'Erythronium', 'Eucomis', 'Fritillaria', 
            'Gagea', 'Galanthus', 'Gasteria', 'Gethyllis', 'Gladiolus', 'Gloriosa', 'Haemanthus', 
            'Haworthia', 'Hemerocallis', 'Herreria', 'Hesperaloe', 'Hippeastrum', 'Hosta', 
            'Hyacinthella', 'Hyacinthoides', 'Hyacinthus', 'Hypoxis', 'Ipheion', 'Iris', 
            'Ixia', 'Kniphofia', 'Lachenalia', 'Leucojum', 'Lilium', 'Maianthemum', 'Muscari', 
            'Narcissus', 'Nothoscordum', 'Ornithogalum', 'Paradisea', 'Paris', 'Polygonatum', 
            'Puschkinia', 'Ruscus', 'Sansevieria', 'Scilla', 'Smilax', 'Trillium', 'Tulipa', 
            'Urginea', 'Veratrum', 'Yucca', 'Zigadenus'
        ],
        'Dioscoreales': ['Dioscorea', 'Tamus', 'Tacca'],
        'Liliales': [
            'Alstroemeria', 'Bomarea', 'Calochortus', 'Cardiocrinum', 'Chamaelirium', 
            'Clintonia', 'Disporum', 'Erythronium', 'Fritillaria', 'Gagea', 'Lilium', 
            'Lloydia', 'Medeola', 'Nomocharis', 'Notholirion', 'Paris', 'Prosartes', 
            'Streptopus', 'Tricyrtis', 'Tulipa', 'Uvularia'
        ],
        'Pandanales': ['Cyclanthus', 'Carludovica', 'Freycinetia', 'Pandanus'],
        'Petrosaviales': ['Japonolirion', 'Petrosavia'],
        'Arecales': [
            # 棕榈科的所有属
            'Acanthophoenix', 'Archontophoenix', 'Areca', 'Arenga', 'Astrocaryum', 'Attalea', 
            'Bactris', 'Balaka', 'Basselinia', 'Beccariophoenix', 'Bentinckia', 'Bismarckia', 
            'Borassodendron', 'Borassus', 'Brahea', 'Brassiophoenix', 'Burretiokentia', 'Butia', 
            'Calamus', 'Calyptrocalyx', 'Calyptronoma', 'Carpentaria', 'Carpoxylon', 'Caryota', 
            'Ceroxylon', 'Chamaedorea', 'Chamaerops', 'Chambeyronia', 'Chelyocarpus', 'Chuniophoenix', 
            'Clinosperma', 'Clinostigma', 'Coccothrinax', 'Cocos', 'Colpothrinax', 'Copernicia', 
            'Corypha', 'Cryosophila', 'Cyphophoenix', 'Cyphosperma', 'Cyrtostachys', 'Deckenia', 
            'Desmoncus', 'Dictyocaryum', 'Dictyosperma', 'Dransfieldia', 'Drymophloeus', 'Dypsis', 
            'Elaeis', 'Eleiodoxa', 'Eugeissona', 'Euterpe', 'Gaussia', 'Geonoma', 'Guihaia', 
            'Hedyscepe', 'Heterospathe', 'Howea', 'Hydriastele', 'Hyophorbe', 'Hyospathe', 
            'Hyphaene', 'Iriartea', 'Johannesteijsmannia', 'Jubaea', 'Jubaeopsis', 'Kentiopsis', 
            'Kerriodoxa', 'Latania', 'Lepidorrhachis', 'Licuala', 'Livistona', 'Lodoicea', 
            'Loxococcus', 'Manicaria', 'Mauritia', 'Mauritiella', 'Maximiliana', 'Medemia', 
            'Metroxylon', 'Nannorrhops', 'Neodypsis', 'Neoveitchia', 'Nephrosperma', 'Normanbya', 
            'Nypa', 'Oenocarpus', 'Oncosperma', 'Orania', 'Parajubaea', 'Phoenix', 'Physokentia', 
            'Pinanga', 'Plectocomia', 'Plectocomiopsis', 'Podococcus', 'Prestoea', 'Pritchardia', 
            'Pseudophoenix', 'Ptychococcus', 'Ptychosperma', 'Raphia', 'Ravenea', 'Reinhardtia', 
            'Rhapis', 'Roystonea', 'Sabal', 'Salacca', 'Satakentia', 'Serenoa', 'Socratea', 
            'Syagrus', 'Synechanthus', 'Thrinax', 'Trachycarpus', 'Trithrinax', 'Veitchia', 
            'Verschaffeltia', 'Wallichia', 'Washingtonia', 'Welfia', 'Wodyetia', 'Zombia'
        ],
        'Commelinales': [
            'Commelina', 'Callisia', 'Gibasis', 'Tradescantia', 'Haemodorum', 'Anigozanthos', 
            'Conostylis', 'Hanguana', 'Philydrum', 'Pontederia', 'Eichhornia', 'Heteranthera', 
            'Monochoria'
        ],
        'Zingiberales': [
            # 姜科、芭蕉科、天堂鸟科等
            'Alpinia', 'Amomum', 'Boesenbergia', 'Costus', 'Curcuma', 'Elettaria', 'Etlingera', 
            'Globba', 'Hedychium', 'Kaempferia', 'Renealmia', 'Zingiber', 'Canna', 'Calathea', 
            'Ctenanthe', 'Goeppertia', 'Ischnosiphon', 'Maranta', 'Monotagma', 'Myrosma', 
            'Sarcophrynium', 'Stromanthe', 'Thalia', 'Thaumatococcus', 'Heliconia', 'Ensete', 
            'Musa', 'Musella', 'Orchidantha', 'Lowia', 'Strelitzia', 'Ravenala', 'Phenakospermum'
        ],
        'Poales': [
            # 禾本科、莎草科、灯心草科、凤梨科等（这里只列出部分）
            'Acidosasa', 'Bambusa', 'Bashania', 'Chimonobambusa', 'Chusquea', 'Dendrocalamus', 
            'Fargesia', 'Gigantochloa', 'Guadua', 'Indocalamus', 'Indosasa', 'Phyllostachys', 
            'Pseudosasa', 'Sasa', 'Semiarundinaria', 'Shibataea', 'Thamnocalamus', 'Yushania',
            'Achnatherum', 'Aegilops', 'Aeluropus', 'Agropyron', 'Agrostis', 'Aira', 'Alopecurus', 
            'Ammophila', 'Andropogon', 'Anemanthele', 'Anthoxanthum', 'Apera', 'Aristida', 
            'Arrhenatherum', 'Arundo', 'Austroderia', 'Austrostipa', 'Avena', 'Avenella', 'Avenula',
            'Carex', 'Cyperus', 'Eleocharis', 'Eriophorum', 'Fimbristylis', 'Fuirena', 
            'Rhynchospora', 'Schoenoplectus', 'Scirpus', 'Scleria', 'Juncus', 'Luzula',
            'Aechmea', 'Ananas', 'Billbergia', 'Bromelia', 'Cryptanthus', 'Dyckia', 'Guzmania', 
            'Hechtia', 'Neoregelia', 'Pitcairnia', 'Puya', 'Tillandsia', 'Vriesea'
        ],
        # 新增兰科目
        'Asparagales_Orchidaceae': [
            # 兰科的主要属
            'Orchis', 'Ophrys', 'Dactylorhiza', 'Platanthera', 'Gymnadenia', 'Anacamptis',
            'Serapias', 'Himantoglossum', 'Aceras', 'Neotinea', 'Orchidaceae'
        ]
    }
    
    # 分析所有物种的属
    genus_counts = Counter()
    assigned_species = set()
    unassigned_species = []
    
    for species in species_list:
        genus = species.split('_')[0]
        genus_counts[genus] += 1
        
        # 检查是否已分组
        assigned = False
        for order_name, genera in monocot_orders_extended.items():
            if genus in genera:
                assigned_species.add(species)
                assigned = True
                break
        
        if not assigned:
            unassigned_species.append(species)
    
    print(f"总物种数: {len(species_list)}")
    print(f"已分组物种: {len(assigned_species)}")
    print(f"未分组物种: {len(unassigned_species)}")
    
    # 分析未分组物种的属分布
    unassigned_genera = Counter()
    for species in unassigned_species:
        genus = species.split('_')[0]
        unassigned_genera[genus] += 1
    
    print(f"\n未分组属的数量: {len(unassigned_genera)}")
    print(f"前20个最常见的未分组属:")
    for genus, count in unassigned_genera.most_common(20):
        print(f"  {genus}: {count} 个物种")
    
    # 识别可能的兰科属
    potential_orchid_genera = []
    for genus, count in unassigned_genera.items():
        # 兰科属通常以特定模式命名
        if any(pattern in genus.lower() for pattern in ['orchid', 'dendro', 'phala', 'cymbid', 'cattley', 'oncid']):
            potential_orchid_genera.append((genus, count))
    
    if potential_orchid_genera:
        print(f"\n可能的兰科属 ({len(potential_orchid_genera)} 个):")
        for genus, count in potential_orchid_genera:
            print(f"  {genus}: {count} 个物种")
    
    return unassigned_species, unassigned_genera

def main():
    """主函数"""
    tree_file = "monocot_taxonomic_skeleton_tree.tre"
    
    if not os.path.exists(tree_file):
        print(f"错误: 文件 {tree_file} 不存在")
        return
    
    print("🔍 分析monocot未分组物种")
    print("=" * 60)
    
    # 提取物种
    species_list = extract_species_from_tree(tree_file)
    
    # 分析未分组物种
    unassigned_species, unassigned_genera = analyze_unassigned_genera(species_list)
    
    # 保存未分组物种列表
    with open("unassigned_monocot_species.txt", "w", encoding='utf-8') as f:
        f.write("未分组的单子叶植物物种\n")
        f.write("=" * 40 + "\n\n")
        for species in sorted(unassigned_species):
            f.write(f"{species}\n")
    
    print(f"\n📄 未分组物种列表已保存到: unassigned_monocot_species.txt")
    
    # 建议改进方案
    print(f"\n💡 改进建议:")
    print(f"1. 大部分未分组物种可能是兰科(Orchidaceae)")
    print(f"2. 可以创建一个'Asparagales_Orchidaceae'分组")
    print(f"3. 或者将所有未分组物种归入'其他单子叶植物'")
    print(f"4. 需要植物分类学专家进一步确认")

if __name__ == "__main__":
    main()
