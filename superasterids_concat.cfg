## ALIGNMENT FILE ##
alignment = concat.phy;

## BRANCHLENGTHS: linked | unlinked ##
branchlengths = linked;

## MODELS OF EVOLUTION: all | allx | mrbayes | beast | gamma | gammai | <list> ##
models = GTR, GTR+G, GTR+I+G;

# MODEL SELECTION: AIC | AICc | BIC #
model_selection = aicc;

## DATA BLOCKS: see manual for how to define ##
[data_blocks]
msa_18S_pos1 = 1-1532\3;
msa_18S_pos2 = 2-1532\3;
msa_18S_pos3 = 3-1532\3;
msa_28S_pos1 = 1533-5308\3;
msa_28S_pos2 = 1534-5308\3;
msa_28S_pos3 = 1535-5308\3;
msa_ITS_pos1 = 5309-5680\3;
msa_ITS_pos2 = 5310-5680\3;
msa_ITS_pos3 = 5311-5680\3;
msa_accD_pos1 = 5681-7213\3;
msa_accD_pos2 = 5682-7213\3;
msa_accD_pos3 = 5683-7213\3;
msa_atpA_pos1 = 7214-8705\3;
msa_atpA_pos2 = 7215-8705\3;
msa_atpA_pos3 = 7216-8705\3;
msa_atpB_pos1 = 8706-10200\3;
msa_atpB_pos2 = 8707-10200\3;
msa_atpB_pos3 = 8708-10200\3;
msa_atpE_pos1 = 10201-10599\3;
msa_atpE_pos2 = 10202-10599\3;
msa_atpE_pos3 = 10203-10599\3;
msa_atpF_pos1 = 10600-11154\3;
msa_atpF_pos2 = 10601-11154\3;
msa_atpF_pos3 = 10602-11154\3;
msa_atpH_pos1 = 11155-11403\3;
msa_atpH_pos2 = 11156-11403\3;
msa_atpH_pos3 = 11157-11403\3;
msa_atpI_pos1 = 11404-12122\3;
msa_atpI_pos2 = 11405-12122\3;
msa_atpI_pos3 = 11406-12122\3;
msa_ccsA_pos1 = 12123-13087\3;
msa_ccsA_pos2 = 12124-13087\3;
msa_ccsA_pos3 = 12125-13087\3;
msa_cemA_pos1 = 13088-13776\3;
msa_cemA_pos2 = 13089-13776\3;
msa_cemA_pos3 = 13090-13776\3;
msa_clpP_pos1 = 13777-14367\3;
msa_clpP_pos2 = 13778-14367\3;
msa_clpP_pos3 = 13779-14367\3;

## SCHEMES, search: all | user | greedy | rcluster | rclusterf | kmeans ##
[schemes]
search = greedy;