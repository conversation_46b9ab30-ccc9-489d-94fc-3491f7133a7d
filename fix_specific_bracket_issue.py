#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复IQ-TREE的"冗余双重括号"问题
保持骨架树的拓扑结构
"""

import re
import os

def fix_iqtree_bracket_issues(tree_string):
    """
    修复IQ-TREE特定的括号问题，保持拓扑结构
    """
    print("修复IQ-TREE括号问题...")
    
    original_tree = tree_string.strip()
    if not original_tree.endswith(';'):
        original_tree += ';'
    
    fixed_tree = original_tree
    changes = []
    
    # 1. 检查并修复最外层的冗余括号
    # 如果整个树被多层括号包围，可能需要减少层数
    while (fixed_tree.startswith('((') and 
           fixed_tree.endswith('));') and 
           len(fixed_tree) > 10):
        
        # 检查是否可以安全移除最外层括号
        inner_content = fixed_tree[1:-2]  # 移除最外层的 ( 和 );
        
        # 验证内容的括号平衡
        depth = 0
        min_depth = 0
        for char in inner_content:
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            min_depth = min(min_depth, depth)
        
        # 如果最小深度为负，说明不能移除最外层括号
        if min_depth < 0 or depth != 0:
            break
        
        # 计算顶层的逗号数（不在括号内的逗号）
        top_level_commas = 0
        depth = 0
        for char in inner_content:
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            elif char == ',' and depth == 0:
                top_level_commas += 1
        
        # 如果顶层只有一个分支，移除最外层括号
        if top_level_commas == 0:
            fixed_tree = inner_content + ';'
            changes.append("移除一层冗余的最外层括号")
            print(f"  移除了一层最外层括号")
        else:
            break
    
    # 2. 修复特定的问题模式
    
    # 修复 ))),( 模式 - 这是IQ-TREE最敏感的
    pattern1 = r'\)\)\),\('
    matches1 = list(re.finditer(pattern1, fixed_tree))
    if matches1:
        print(f"  发现 {len(matches1)} 处 ))),( 模式")
        # 将 ))),( 替换为 )),( 
        fixed_tree = re.sub(pattern1, ')),(' , fixed_tree)
        changes.append(f"修复 {len(matches1)} 处 ))),( 模式")
    
    # 修复 ((( 开头的过度嵌套
    pattern2 = r'^\(\(\(\(\(\(\(\(\(\(\(\(\('
    if re.match(pattern2, fixed_tree):
        print("  发现开头有过多连续括号")
        # 减少开头的括号数量
        fixed_tree = re.sub(r'^\(\(\(\(\(\(\(\(\(\(\(\(\(', '((((((((((', fixed_tree)
        # 相应地减少结尾的括号
        fixed_tree = re.sub(r'\)\)\)\)\)\)\)\)\)\)\)\)\);$', '))))))))));', fixed_tree)
        changes.append("减少开头和结尾的过度嵌套括号")
    
    # 3. 验证修复结果
    open_count = fixed_tree.count('(')
    close_count = fixed_tree.count(')')
    
    print(f"  修复前括号: {original_tree.count('(')} 开, {original_tree.count(')')} 关")
    print(f"  修复后括号: {open_count} 开, {close_count} 关")
    print(f"  括号平衡: {'是' if open_count == close_count else '否'}")
    
    # 验证物种数量保持不变
    species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
    orig_species = set(re.findall(species_pattern, original_tree))
    fixed_species = set(re.findall(species_pattern, fixed_tree))
    
    print(f"  物种数量: 原始 {len(orig_species)}, 修复后 {len(fixed_species)}")
    
    if len(orig_species) != len(fixed_species):
        print("  ⚠️ 警告: 物种数量发生变化!")
        return original_tree, []
    
    return fixed_tree, changes

def create_conservative_fix(input_file, output_file):
    """
    创建保守的修复版本
    """
    print(f"保守修复: {input_file} -> {output_file}")
    print("-" * 50)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        print(f"原始文件大小: {len(original_tree)} 字符")
        
        # 应用修复
        fixed_tree, changes = fix_iqtree_bracket_issues(original_tree)
        
        if changes:
            print(f"应用的修复: {', '.join(changes)}")
            
            # 保存修复后的文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(fixed_tree)
            
            print(f"✅ 保守修复完成: {output_file}")
            return True
        else:
            print("未进行任何修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def create_minimal_structure_fix(input_file, output_file):
    """
    创建最小结构修复版本 - 保持拓扑但简化格式
    """
    print(f"最小结构修复: {input_file} -> {output_file}")
    print("-" * 50)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 提取物种
        species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
        all_species = re.findall(species_pattern, original_tree)
        unique_species = list(dict.fromkeys(all_species))  # 保持顺序去重
        
        print(f"提取到 {len(unique_species)} 个物种")
        
        # 分析原始树的主要分组
        # 这是一个简化的方法，尝试保持一些结构
        
        # 找到主要的分割点
        # 通过分析逗号在不同括号深度的分布来确定主要分组
        depth_commas = []
        depth = 0
        for i, char in enumerate(original_tree):
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            elif char == ',' and depth <= 3:  # 只考虑较浅层的逗号
                depth_commas.append((i, depth))
        
        # 如果有浅层分组，尝试保持
        if len(depth_commas) > 0 and len(unique_species) > 10:
            # 简单的二分法
            mid = len(unique_species) // 2
            group1 = unique_species[:mid]
            group2 = unique_species[mid:]
            
            # 创建简单的二分树
            minimal_tree = f"(({','.join(group1)}),({','.join(group2)}));"
        else:
            # 如果无法分组，创建星形树但用二分包装
            if len(unique_species) <= 2:
                minimal_tree = f"({','.join(unique_species)});"
            else:
                # 三分组
                third = len(unique_species) // 3
                group1 = unique_species[:third]
                group2 = unique_species[third:2*third]
                group3 = unique_species[2*third:]
                
                minimal_tree = f"(({','.join(group1)}),({','.join(group2)}),({','.join(group3)}));"
        
        # 验证新树
        new_open = minimal_tree.count('(')
        new_close = minimal_tree.count(')')
        
        print(f"新树括号: {new_open} 开, {new_close} 关")
        print(f"新树长度: {len(minimal_tree)} 字符")
        
        # 保存
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(minimal_tree)
        
        print(f"✅ 最小结构修复完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 最小结构修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    input_files = [
        "superrosids_iqtree_fixed.tre",
        "superrosids_species_tree.tre",
        "ANA_species_tree.tre",
        "dicots_species_tree.tre",
        "superasterids_species_tree.tre"
    ]
    
    print("🔧 专门修复IQ-TREE骨架树括号问题")
    print("目标: 保持拓扑结构，修复格式问题")
    print("=" * 80)
    
    for input_file in input_files:
        if os.path.exists(input_file):
            base_name = input_file.replace('_species_tree.tre', '').replace('_iqtree_fixed.tre', '')
            
            # 方案1: 保守修复
            conservative_output = f"{base_name}_CONSERVATIVE.tre"
            success1 = create_conservative_fix(input_file, conservative_output)
            
            # 方案2: 最小结构修复
            minimal_output = f"{base_name}_MINIMAL.tre"
            success2 = create_minimal_structure_fix(input_file, minimal_output)
            
            if success1 or success2:
                print(f"\n💡 建议测试顺序:")
                if success1:
                    print(f"1. iqtree2 -s concat.phy -p {base_name}_concat.txt -t {conservative_output} -m MFP -bb 1000")
                if success2:
                    print(f"2. iqtree2 -s concat.phy -p {base_name}_concat.txt -t {minimal_output} -m MFP -bb 1000")
            
            print()
        else:
            print(f"⚠️ 文件不存在: {input_file}")
    
    print("🎯 如果以上都失败，最后的选择:")
    print("iqtree2 -s concat.phy -p superrosids_concat.txt -m MFP -bb 1000  # 无约束树")

if __name__ == "__main__":
    main()
