## ALIGNMENT FILE ##
alignment = concat.phy;

## BRANCHLENGTHS: linked | unlinked ##
branchlengths = linked;

## MODELS OF EVOLUTION: all | allx | mrbayes | beast | gamma | gammai | <list> ##
models = GTR, GTR+G, GTR+I+G;

# MODEL SELECTION: AIC | AICc | BIC #
model_selection = aicc;

## DATA BLOCKS: see manual for how to define ##
[data_blocks]
msa_18S_pos1 = 1-1555\3;
msa_18S_pos2 = 2-1555\3;
msa_18S_pos3 = 3-1555\3;
msa_28S_pos1 = 1556-2612\3;
msa_28S_pos2 = 1557-2612\3;
msa_28S_pos3 = 1558-2612\3;
msa_ITS_pos1 = 2613-2845\3;
msa_ITS_pos2 = 2614-2845\3;
msa_ITS_pos3 = 2615-2845\3;
msa_accD_pos1 = 2846-4335\3;
msa_accD_pos2 = 2847-4335\3;
msa_accD_pos3 = 2848-4335\3;
msa_atpA_pos1 = 4336-5928\3;
msa_atpA_pos2 = 4337-5928\3;
msa_atpA_pos3 = 4338-5928\3;
msa_atpB_pos1 = 5929-7425\3;
msa_atpB_pos2 = 5930-7425\3;
msa_atpB_pos3 = 5931-7425\3;
msa_atpE_pos1 = 7426-7824\3;
msa_atpE_pos2 = 7427-7824\3;
msa_atpE_pos3 = 7428-7824\3;
msa_atpF_pos1 = 7825-8367\3;
msa_atpF_pos2 = 7826-8367\3;
msa_atpF_pos3 = 7827-8367\3;
msa_atpH_pos1 = 8368-8613\3;
msa_atpH_pos2 = 8369-8613\3;
msa_atpH_pos3 = 8370-8613\3;
msa_atpI_pos1 = 8614-9356\3;
msa_atpI_pos2 = 8615-9356\3;
msa_atpI_pos3 = 8616-9356\3;
msa_ccsA_pos1 = 9357-10514\3;
msa_ccsA_pos2 = 9358-10514\3;
msa_ccsA_pos3 = 9359-10514\3;
msa_cemA_pos1 = 10515-11200\3;
msa_cemA_pos2 = 10516-11200\3;
msa_cemA_pos3 = 10517-11200\3;
msa_clpP_pos1 = 11201-11791\3;
msa_clpP_pos2 = 11202-11791\3;
msa_clpP_pos3 = 11203-11791\3;
msa_matK_pos1 = 11792-13305\3;
msa_matK_pos2 = 11793-13305\3;
msa_matK_pos3 = 11794-13305\3;
msa_ndhA_pos1 = 13306-14394\3;
msa_ndhA_pos2 = 13307-14394\3;
msa_ndhA_pos3 = 13308-14394\3;
msa_ndhB_pos1 = 14395-15860\3;
msa_ndhB_pos2 = 14396-15860\3;
msa_ndhB_pos3 = 14397-15860\3;
msa_ndhC_pos1 = 15861-16223\3;
msa_ndhC_pos2 = 15862-16223\3;
msa_ndhC_pos3 = 15863-16223\3;
msa_ndhD_pos1 = 16224-17714\3;
msa_ndhD_pos2 = 16225-17714\3;
msa_ndhD_pos3 = 16226-17714\3;
msa_ndhE_pos1 = 17715-18014\3;
msa_ndhE_pos2 = 17716-18014\3;
msa_ndhE_pos3 = 17717-18014\3;
msa_ndhF_pos1 = 18015-20229\3;
msa_ndhF_pos2 = 18016-20229\3;
msa_ndhF_pos3 = 18017-20229\3;
msa_ndhG_pos1 = 20230-20760\3;
msa_ndhG_pos2 = 20231-20760\3;
msa_ndhG_pos3 = 20232-20760\3;
msa_ndhH_pos1 = 20761-21941\3;
msa_ndhH_pos2 = 20762-21941\3;
msa_ndhH_pos3 = 20763-21941\3;
msa_ndhI_pos1 = 21942-22414\3;
msa_ndhI_pos2 = 21943-22414\3;
msa_ndhI_pos3 = 21944-22414\3;
msa_ndhJ_pos1 = 22415-22891\3;
msa_ndhJ_pos2 = 22416-22891\3;
msa_ndhJ_pos3 = 22417-22891\3;
msa_ndhK_pos1 = 22892-23569\3;
msa_ndhK_pos2 = 22893-23569\3;
msa_ndhK_pos3 = 22894-23569\3;
msa_petA_pos1 = 23570-24532\3;
msa_petA_pos2 = 23571-24532\3;
msa_petA_pos3 = 23572-24532\3;
msa_petB_pos1 = 24533-25078\3;
msa_petB_pos2 = 24534-25078\3;
msa_petB_pos3 = 24535-25078\3;
msa_petD_pos1 = 25079-25560\3;
msa_petD_pos2 = 25080-25560\3;
msa_petD_pos3 = 25081-25560\3;
msa_petG_pos1 = 25561-25674\3;
msa_petG_pos2 = 25562-25674\3;
msa_petG_pos3 = 25563-25674\3;
msa_petL_pos1 = 25675-25770\3;
msa_petL_pos2 = 25676-25770\3;
msa_petL_pos3 = 25677-25770\3;
msa_petN_pos1 = 25771-25860\3;
msa_petN_pos2 = 25772-25860\3;
msa_petN_pos3 = 25773-25860\3;
msa_psaA_pos1 = 25861-28112\3;
msa_psaA_pos2 = 25862-28112\3;
msa_psaA_pos3 = 25863-28112\3;
msa_psaB_pos1 = 28113-29669\3;
msa_psaB_pos2 = 28114-29669\3;
msa_psaB_pos3 = 28115-29669\3;
msa_psaC_pos1 = 29670-29898\3;
msa_psaC_pos2 = 29671-29898\3;
msa_psaC_pos3 = 29672-29898\3;
msa_psaI_pos1 = 29899-30002\3;
msa_psaI_pos2 = 29900-30002\3;
msa_psaI_pos3 = 29901-30002\3;
msa_psaJ_pos1 = 30003-30130\3;
msa_psaJ_pos2 = 30004-30130\3;
msa_psaJ_pos3 = 30005-30130\3;

## SCHEMES, search: all | user | greedy | rcluster | rclusterf | kmeans ##
[schemes]
search = greedy;