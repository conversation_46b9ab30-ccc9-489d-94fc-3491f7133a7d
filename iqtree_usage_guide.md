# IQ-TREE使用指南 - ANA分类分析

## 🎯 问题解决

**原始错误**: `ERROR: Redundant double-bracket '((…))' with closing bracket ending at (line 1 column 34)`

**解决方案**: 使用修复后的树文件

## 📁 可用文件

### 1. 分区文件
- **ANA_concat.txt** - 分区定义文件，包含84个基因的位置信息

### 2. 树文件（已修复）
- **ANA_species_tree_fixed.tre** - 修复后的原始树结构（推荐）
- **ANA_species_tree_simple.tre** - 简单星形树（备选）

### 3. 序列文件
- **concat.phy** - 连接的序列文件（假设存在）

## 🚀 IQ-TREE命令示例

### 基本分析（使用修复后的树）
```bash
iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_fixed.tre -m MFP -bb 1000 -nt AUTO
```

### 不使用约束树的分析
```bash
iqtree2 -s concat.phy -p ANA_concat.txt -m MFP -bb 1000 -nt AUTO
```

### 使用简单星形树
```bash
iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_simple.tre -m MFP -bb 1000 -nt AUTO
```

### 模型测试
```bash
iqtree2 -s concat.phy -p ANA_concat.txt -m TESTONLY -nt AUTO
```

## 📋 参数说明

| 参数 | 说明 |
|------|------|
| `-s` | 序列文件 |
| `-p` | 分区文件 |
| `-t` | 约束树文件 |
| `-m MFP` | 自动选择最佳模型 |
| `-bb 1000` | 1000次bootstrap |
| `-nt AUTO` | 自动选择线程数 |

## 🔧 故障排除

### 如果仍然出现树文件错误：
1. **使用简单星形树**:
   ```bash
   iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_simple.tre -m MFP -bb 1000
   ```

2. **不使用约束树**:
   ```bash
   iqtree2 -s concat.phy -p ANA_concat.txt -m MFP -bb 1000
   ```

3. **检查序列文件中的物种名称**:
   确保序列文件中的物种名称与树文件中的名称完全匹配

### 如果出现分区文件错误：
1. **检查序列长度**:
   ```bash
   # 确保concat.phy的序列长度与ANA_concat.txt中的最大位置匹配
   ```

2. **使用单一模型**:
   ```bash
   iqtree2 -s concat.phy -m GTR+G -bb 1000 -nt AUTO
   ```

## 📊 文件验证

### 检查树文件格式
```bash
# 使用任何文本编辑器检查树文件
# 确保：
# 1. 只有一行内容
# 2. 以分号(;)结尾
# 3. 括号匹配
```

### 检查分区文件格式
```bash
# ANA_concat.txt应该包含如下格式的行：
# DNA, 18S = 1-1809
# DNA, 28S = 1810-5147
# ...
```

## 🎯 推荐工作流程

1. **首先尝试使用修复后的树**:
   ```bash
   iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_fixed.tre -m MFP -bb 1000 -nt AUTO
   ```

2. **如果失败，使用简单星形树**:
   ```bash
   iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_simple.tre -m MFP -bb 1000 -nt AUTO
   ```

3. **如果仍然失败，不使用约束树**:
   ```bash
   iqtree2 -s concat.phy -p ANA_concat.txt -m MFP -bb 1000 -nt AUTO
   ```

## 📈 预期结果

- **输出文件**: `concat.phy.treefile` - 最终的系统发育树
- **Bootstrap支持**: `concat.phy.contree` - 带bootstrap值的一致性树
- **模型信息**: `concat.phy.iqtree` - 详细的分析日志

## 💡 提示

1. **树文件问题已解决**: 使用 `ANA_species_tree_fixed.tre`
2. **分区分析**: 84个基因分区将提供更准确的模型选择
3. **物种数量**: ANA分类包含187个物种
4. **序列长度**: 总长度66,347个碱基对

## 🆘 如果还有问题

请检查：
1. 序列文件格式是否正确
2. 物种名称是否在序列文件和树文件中完全匹配
3. IQ-TREE版本是否为最新版本

---
*生成时间: 2024年6月29日*
*工具: 树格式修复脚本*
