#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将当前路径下所有FASTA文件名称（不含.fasta后缀）输出为CSV文件
"""

import os
import csv
from datetime import datetime

def export_fasta_names_to_csv():
    """
    扫描当前路径下的所有FASTA文件，将文件名（不含.fasta后缀）导出为CSV文件
    """
    # 定义路径和输出文件名
    current_dir = "."
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_csv = f"fasta_filenames_{timestamp}.csv"
    
    print("📁 FASTA文件名导出工具")
    print("=" * 60)
    print(f"📂 扫描路径: {os.path.abspath(current_dir)}")
    print(f"📄 输出文件: {output_csv}")
    print("=" * 60)
    
    # 扫描当前路径下的所有FASTA文件
    fasta_files = []
    all_files = os.listdir(current_dir)
    
    print(f"\n🔍 正在扫描FASTA文件...")
    
    for filename in all_files:
        file_path = os.path.join(current_dir, filename)
        # 检查是否为文件且以.fasta结尾
        if os.path.isfile(file_path) and filename.lower().endswith('.fasta'):
            # 移除.fasta后缀
            name_without_extension = filename[:-6]  # 移除最后6个字符(.fasta)
            fasta_files.append(name_without_extension)
            print(f"   ✓ 找到: {filename} -> {name_without_extension}")
    
    print(f"\n📊 统计结果:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   FASTA文件数: {len(fasta_files)}")
    
    if len(fasta_files) == 0:
        print("\n⚠️ 警告: 当前路径下没有找到FASTA文件！")
        print("请确保:")
        print("   1. 当前路径下有.fasta文件")
        print("   2. 文件扩展名为.fasta（不区分大小写）")
        return False
    
    # 对文件名进行排序
    fasta_files.sort()
    
    try:
        # 写入CSV文件
        print(f"\n💾 正在写入CSV文件...")
        with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(['序号', '文件名'])
            
            # 写入数据
            for i, filename in enumerate(fasta_files, 1):
                writer.writerow([i, filename])
                
        print(f"✅ CSV文件创建成功!")
        print(f"📄 文件路径: {os.path.abspath(output_csv)}")
        
        # 显示前几个文件名作为预览
        print(f"\n📋 文件名预览（前10个）:")
        for i, filename in enumerate(fasta_files[:10], 1):
            print(f"   {i:2d}. {filename}")
        
        if len(fasta_files) > 10:
            print(f"   ... 还有 {len(fasta_files) - 10} 个文件")
            
        return True
        
    except Exception as e:
        print(f"❌ 写入CSV文件时出错: {str(e)}")
        return False

def export_simple_list():
    """
    导出简单的文件名列表（每行一个文件名，无序号）
    """
    current_dir = "."
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_txt = f"fasta_filenames_simple_{timestamp}.txt"
    
    # 获取FASTA文件名
    fasta_files = []
    for filename in os.listdir(current_dir):
        file_path = os.path.join(current_dir, filename)
        if os.path.isfile(file_path) and filename.lower().endswith('.fasta'):
            name_without_extension = filename[:-6]
            fasta_files.append(name_without_extension)
    
    if len(fasta_files) == 0:
        return False
    
    fasta_files.sort()
    
    try:
        # 写入简单文本文件
        with open(output_txt, 'w', encoding='utf-8') as txtfile:
            for filename in fasta_files:
                txtfile.write(filename + '\n')
        
        print(f"📄 同时创建了简单列表文件: {output_txt}")
        return True
        
    except Exception as e:
        print(f"❌ 写入文本文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        print("🧬 FASTA文件名导出工具")
        print("功能: 扫描当前路径下的所有FASTA文件，导出文件名列表\n")
        
        # 导出CSV格式
        csv_success = export_fasta_names_to_csv()
        
        if csv_success:
            # 同时导出简单文本格式
            export_simple_list()
            
            print(f"\n🎉 导出完成!")
            print(f"💡 提示:")
            print(f"   • CSV文件包含序号和文件名两列")
            print(f"   • TXT文件只包含文件名列表")
            print(f"   • 所有文件名都已移除.fasta后缀")
            print(f"   • 文件名按字母顺序排序")
        else:
            print(f"\n⚠️ 导出失败，请检查当前路径下是否有FASTA文件")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
