#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复monocot_taxonomic_skeleton_tree.tre文件的格式问题
"""

import re
import os

def analyze_monocot_tree_problem(tree_string):
    """
    分析monocot树的具体问题
    """
    print("分析monocot树的问题...")
    print(f"树长度: {len(tree_string)} 字符")
    
    # 检查第93151个字符附近的内容
    error_pos = 93151
    start_pos = max(0, error_pos - 50)
    end_pos = min(len(tree_string), error_pos + 50)
    
    print(f"错误位置 {error_pos} 附近的内容:")
    print(f"'{tree_string[start_pos:end_pos]}'")
    
    # 分析括号结构
    open_count = tree_string.count('(')
    close_count = tree_string.count(')')
    
    print(f"括号统计:")
    print(f"  开括号: {open_count}")
    print(f"  关括号: {close_count}")
    print(f"  差值: {open_count - close_count}")
    
    # 检查开头的括号模式
    opening_pattern = ""
    for i, char in enumerate(tree_string[:20]):
        if char == '(':
            opening_pattern += char
        else:
            break
    
    print(f"开头括号模式: '{opening_pattern}' (共{len(opening_pattern)}个)")
    
    return error_pos, open_count, close_count

def extract_species_from_monocot_tree(tree_string):
    """
    从monocot树中提取所有物种名称
    """
    # 使用正则表达式提取物种名称
    species_pattern = r'\b([A-Z][a-z]+_[a-z-]+)\b'
    species_matches = re.findall(species_pattern, tree_string)
    
    # 去重并排序
    unique_species = sorted(list(set(species_matches)))
    
    print(f"提取到 {len(unique_species)} 个物种")
    
    return unique_species

def create_monocot_taxonomic_tree(species_list):
    """
    基于单子叶植物分类学创建正确的骨架树
    """
    print("创建单子叶植物分类学骨架树...")
    
    # 单子叶植物主要目的分类
    monocot_orders = {
        'Acorales': ['Acorus'],
        'Alismatales': ['Alisma', 'Aponogeton', 'Butomus', 'Hydrocharis', 'Potamogeton', 'Sagittaria', 'Vallisneria', 'Zannichellia', 'Zostera', 'Cymodocea', 'Halophila', 'Najas', 'Ruppia', 'Scheuchzeria', 'Triglochin', 'Araceae', 'Alocasia', 'Amorphophallus', 'Anthurium', 'Arisaema', 'Arisarum', 'Arum', 'Caladium', 'Calla', 'Colocasia', 'Cryptocoryne', 'Dieffenbachia', 'Dracunculus', 'Epipremnum', 'Homalomena', 'Monstera', 'Philodendron', 'Pistia', 'Spathiphyllum', 'Symplocarpus', 'Xanthosoma', 'Zantedeschia'],
        'Asparagales': ['Agapanthus', 'Agave', 'Albuca', 'Allium', 'Aloe', 'Amaryllis', 'Anthericum', 'Asparagus', 'Asphodeline', 'Asphodelus', 'Aspidistra', 'Bellevalia', 'Bowiea', 'Brodiaea', 'Bulbine', 'Camassia', 'Chlorophytum', 'Colchicum', 'Convallaria', 'Cordyline', 'Crinum', 'Crocus', 'Curculigo', 'Cyrtanthus', 'Dianella', 'Dipcadi', 'Dracaena', 'Drimia', 'Drimiopsis', 'Erythronium', 'Eucomis', 'Fritillaria', 'Gagea', 'Galanthus', 'Gasteria', 'Gethyllis', 'Gladiolus', 'Gloriosa', 'Haemanthus', 'Haworthia', 'Hemerocallis', 'Herreria', 'Hesperaloe', 'Hippeastrum', 'Hosta', 'Hyacinthella', 'Hyacinthoides', 'Hyacinthus', 'Hypoxis', 'Ipheion', 'Iris', 'Ixia', 'Kniphofia', 'Lachenalia', 'Leucojum', 'Lilium', 'Maianthemum', 'Muscari', 'Narcissus', 'Nothoscordum', 'Ornithogalum', 'Paradisea', 'Paris', 'Polygonatum', 'Puschkinia', 'Ruscus', 'Sansevieria', 'Scilla', 'Smilax', 'Trillium', 'Tulipa', 'Urginea', 'Veratrum', 'Yucca', 'Zigadenus'],
        'Dioscoreales': ['Dioscorea', 'Tamus', 'Tacca'],
        'Liliales': ['Alstroemeria', 'Bomarea', 'Calochortus', 'Cardiocrinum', 'Chamaelirium', 'Clintonia', 'Disporum', 'Erythronium', 'Fritillaria', 'Gagea', 'Lilium', 'Lloydia', 'Medeola', 'Nomocharis', 'Notholirion', 'Paris', 'Prosartes', 'Streptopus', 'Tricyrtis', 'Tulipa', 'Uvularia'],
        'Pandanales': ['Cyclanthus', 'Carludovica', 'Freycinetia', 'Pandanus'],
        'Petrosaviales': ['Japonolirion', 'Petrosavia'],
        'Arecales': ['Acanthophoenix', 'Archontophoenix', 'Areca', 'Arenga', 'Astrocaryum', 'Attalea', 'Bactris', 'Balaka', 'Basselinia', 'Beccariophoenix', 'Bentinckia', 'Bismarckia', 'Borassodendron', 'Borassus', 'Brahea', 'Brassiophoenix', 'Burretiokentia', 'Butia', 'Calamus', 'Calyptrocalyx', 'Calyptronoma', 'Carpentaria', 'Carpoxylon', 'Caryota', 'Ceroxylon', 'Chamaedorea', 'Chamaerops', 'Chambeyronia', 'Chelyocarpus', 'Chuniophoenix', 'Clinosperma', 'Clinostigma', 'Coccothrinax', 'Cocos', 'Colpothrinax', 'Copernicia', 'Corypha', 'Cryosophila', 'Cyphophoenix', 'Cyphosperma', 'Cyrtostachys', 'Deckenia', 'Desmoncus', 'Dictyocaryum', 'Dictyosperma', 'Dransfieldia', 'Drymophloeus', 'Dypsis', 'Elaeis', 'Eleiodoxa', 'Eugeissona', 'Euterpe', 'Gaussia', 'Geonoma', 'Guihaia', 'Hedyscepe', 'Heterospathe', 'Howea', 'Hydriastele', 'Hyophorbe', 'Hyospathe', 'Hyphaene', 'Iriartea', 'Johannesteijsmannia', 'Jubaea', 'Jubaeopsis', 'Kentiopsis', 'Kerriodoxa', 'Latania', 'Lepidorrhachis', 'Licuala', 'Livistona', 'Lodoicea', 'Loxococcus', 'Manicaria', 'Mauritia', 'Mauritiella', 'Maximiliana', 'Medemia', 'Metroxylon', 'Nannorrhops', 'Neodypsis', 'Neoveitchia', 'Nephrosperma', 'Normanbya', 'Nypa', 'Oenocarpus', 'Oncosperma', 'Orania', 'Parajubaea', 'Phoenix', 'Physokentia', 'Pinanga', 'Plectocomia', 'Plectocomiopsis', 'Podococcus', 'Prestoea', 'Pritchardia', 'Pseudophoenix', 'Ptychococcus', 'Ptychosperma', 'Raphia', 'Ravenea', 'Reinhardtia', 'Rhapis', 'Roystonea', 'Sabal', 'Salacca', 'Satakentia', 'Serenoa', 'Socratea', 'Syagrus', 'Synechanthus', 'Thrinax', 'Trachycarpus', 'Trithrinax', 'Veitchia', 'Verschaffeltia', 'Wallichia', 'Washingtonia', 'Welfia', 'Wodyetia', 'Zombia'],
        'Commelinales': ['Commelina', 'Callisia', 'Gibasis', 'Tradescantia', 'Haemodorum', 'Anigozanthos', 'Conostylis', 'Hanguana', 'Philydrum', 'Pontederia', 'Eichhornia', 'Heteranthera', 'Monochoria'],
        'Zingiberales': ['Alpinia', 'Amomum', 'Boesenbergia', 'Costus', 'Curcuma', 'Elettaria', 'Etlingera', 'Globba', 'Hedychium', 'Kaempferia', 'Renealmia', 'Zingiber', 'Canna', 'Calathea', 'Ctenanthe', 'Goeppertia', 'Ischnosiphon', 'Maranta', 'Monotagma', 'Myrosma', 'Sarcophrynium', 'Stromanthe', 'Thalia', 'Thaumatococcus', 'Heliconia', 'Ensete', 'Musa', 'Musella', 'Orchidantha', 'Lowia', 'Strelitzia', 'Ravenala', 'Phenakospermum'],
        'Poales': ['Acidosasa', 'Bambusa', 'Bashania', 'Chimonobambusa', 'Chusquea', 'Dendrocalamus', 'Fargesia', 'Gigantochloa', 'Guadua', 'Indocalamus', 'Indosasa', 'Phyllostachys', 'Pseudosasa', 'Sasa', 'Semiarundinaria', 'Shibataea', 'Thamnocalamus', 'Yushania', 'Achnatherum', 'Aegilops', 'Aeluropus', 'Agropyron', 'Agrostis', 'Aira', 'Alopecurus', 'Ammophila', 'Andropogon', 'Anemanthele', 'Anthoxanthum', 'Apera', 'Aristida', 'Arrhenatherum', 'Arundo', 'Austroderia', 'Austrostipa', 'Avena', 'Avenella', 'Avenula', 'Beckmannia', 'Brachyelytrum', 'Brachypodium', 'Briza', 'Bromus', 'Calamagrostis', 'Catabrosa', 'Catapodium', 'Cenchrus', 'Centotheca', 'Chasmanthium', 'Chionochloa', 'Chloris', 'Chrysopogon', 'Cinna', 'Cleistogenes', 'Coleanthus', 'Colpodium', 'Corynephorus', 'Cynodon', 'Cynosurus', 'Dactylis', 'Dactyloctenium', 'Danthonia', 'Dasypyrum', 'Deschampsia', 'Desmazeria', 'Dichanthelium', 'Dichanthium', 'Digitaria', 'Echinochloa', 'Echinopogon', 'Ehrharta', 'Eleusine', 'Elymus', 'Enteropogon', 'Eragrostis', 'Eremochloa', 'Eremopyrum', 'Eriochloa', 'Festuca', 'Gaudinia', 'Glyceria', 'Harpachne', 'Helictochloa', 'Helictotrichon', 'Hesperostipa', 'Heteranthelium', 'Holcus', 'Hordelymus', 'Hordeum', 'Hygroryza', 'Hyparrhenia', 'Imperata', 'Lagurus', 'Lamarckia', 'Leersia', 'Lepturus', 'Leymus', 'Lolium', 'Melica', 'Milium', 'Miscanthus', 'Molinia', 'Muhlenbergia', 'Nardus', 'Nassella', 'Oryzopsis', 'Panicum', 'Parapholis', 'Paspalum', 'Pennisetum', 'Phalaris', 'Phleum', 'Phragmites', 'Piptatherum', 'Poa', 'Polypogon', 'Pseudoroegneria', 'Puccinellia', 'Saccharum', 'Schismus', 'Secale', 'Setaria', 'Sorghum', 'Spartina', 'Sphenopholis', 'Sporobolus', 'Stipa', 'Themeda', 'Trisetum', 'Triticum', 'Urochloa', 'Vulpia', 'Zea', 'Zizania', 'Carex', 'Cyperus', 'Eleocharis', 'Eriophorum', 'Fimbristylis', 'Fuirena', 'Rhynchospora', 'Schoenoplectus', 'Scirpus', 'Scleria', 'Juncus', 'Luzula', 'Aechmea', 'Ananas', 'Billbergia', 'Bromelia', 'Cryptanthus', 'Dyckia', 'Guzmania', 'Hechtia', 'Neoregelia', 'Pitcairnia', 'Puya', 'Tillandsia', 'Vriesea', 'Typha', 'Sparganium', 'Eriocaulon', 'Xyris', 'Mayaca', 'Rapatea', 'Centrolepis', 'Restio', 'Elegia', 'Thamnochortus', 'Anarthria', 'Flagellaria', 'Joinvillea']
    }
    
    # 将物种分配到主要目
    grouped_species = {}
    unassigned_species = []
    
    for species in species_list:
        genus = species.split('_')[0]
        assigned = False
        
        for order_name, genera in monocot_orders.items():
            if genus in genera:
                if order_name not in grouped_species:
                    grouped_species[order_name] = []
                grouped_species[order_name].append(species)
                assigned = True
                break
        
        if not assigned:
            unassigned_species.append(species)
    
    print(f"分组结果:")
    for order_name, species_in_order in grouped_species.items():
        print(f"  {order_name}: {len(species_in_order)} 个物种")
    
    if unassigned_species:
        print(f"  未分组: {len(unassigned_species)} 个物种")
    
    # 创建分层的树结构
    order_parts = []
    
    # 按照单子叶植物的系统发育关系排序
    order_sequence = ['Acorales', 'Alismatales', 'Petrosaviales', 'Dioscoreales', 'Pandanales', 'Liliales', 'Asparagales', 'Arecales', 'Poales', 'Commelinales', 'Zingiberales']
    
    for order_name in order_sequence:
        if order_name in grouped_species:
            species_in_order = sorted(grouped_species[order_name])
            if len(species_in_order) == 1:
                order_parts.append(species_in_order[0])
            elif len(species_in_order) <= 10:
                order_parts.append(f"({','.join(species_in_order)})")
            else:
                # 对于大的目，进一步分组
                mid = len(species_in_order) // 2
                left_group = species_in_order[:mid]
                right_group = species_in_order[mid:]
                
                left_part = f"({','.join(left_group)})" if len(left_group) > 1 else left_group[0]
                right_part = f"({','.join(right_group)})" if len(right_group) > 1 else right_group[0]
                
                order_parts.append(f"({left_part},{right_part})")
    
    # 添加未分组的物种
    if unassigned_species:
        if len(unassigned_species) <= 10:
            if len(unassigned_species) == 1:
                order_parts.append(unassigned_species[0])
            else:
                order_parts.append(f"({','.join(sorted(unassigned_species))})")
        else:
            # 对未分组的物种也进行分组
            mid = len(unassigned_species) // 2
            left_group = sorted(unassigned_species)[:mid]
            right_group = sorted(unassigned_species)[mid:]
            
            left_part = f"({','.join(left_group)})"
            right_part = f"({','.join(right_group)})"
            
            order_parts.append(f"({left_part},{right_part})")
    
    # 创建最终的树结构
    if len(order_parts) <= 2:
        tree = f"({','.join(order_parts)});"
    elif len(order_parts) <= 4:
        tree = f"({','.join(order_parts)});"
    else:
        # 创建平衡的分层结构
        # 基部类群 vs 核心类群
        basal_orders = order_parts[:3]  # Acorales, Alismatales, Petrosaviales
        core_orders = order_parts[3:]   # 其他所有目
        
        if len(basal_orders) == 1:
            basal_part = basal_orders[0]
        else:
            basal_part = f"({','.join(basal_orders)})"
        
        if len(core_orders) == 1:
            core_part = core_orders[0]
        else:
            core_part = f"({','.join(core_orders)})"
        
        tree = f"({basal_part},{core_part});"
    
    return tree

def main():
    """主函数"""
    input_file = "monocot_taxonomic_skeleton_tree.tre"
    output_file = "monocot_taxonomic_skeleton_tree_fixed.tre"
    
    print("🔧 修复monocot骨架树格式")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 分析问题
        error_pos, open_count, close_count = analyze_monocot_tree_problem(original_tree)
        
        # 提取物种
        species_list = extract_species_from_monocot_tree(original_tree)
        
        # 创建新的分类学骨架树
        new_tree = create_monocot_taxonomic_tree(species_list)
        
        # 保存修复后的树
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(new_tree)
        
        # 验证新树
        new_open_count = new_tree.count('(')
        new_close_count = new_tree.count(')')
        
        print(f"\n✅ 修复完成!")
        print(f"📊 对比结果:")
        print(f"   原始树: {len(original_tree)} 字符, {open_count} 开括号, {close_count} 关括号")
        print(f"   新树: {len(new_tree)} 字符, {new_open_count} 开括号, {new_close_count} 关括号")
        print(f"   括号平衡: {'是' if new_open_count == new_close_count else '否'}")
        print(f"   物种数: {len(species_list)}")
        
        print(f"\n📁 输出文件: {output_file}")
        print(f"💡 使用命令:")
        print(f"iqtree2 -s concat.phy -p monocot_concat.txt -t {output_file} -m MFP -bb 1000")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 monocot骨架树修复完成!")
        else:
            print(f"\n❌ 修复失败")
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
