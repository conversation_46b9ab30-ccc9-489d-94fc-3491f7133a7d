#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复ANA_species_tree.tre文件的Newick格式问题
"""

import re
import os

def analyze_tree_structure(tree_string):
    """
    分析树字符串的括号结构
    """
    open_count = 0
    close_count = 0
    max_depth = 0
    current_depth = 0
    
    for i, char in enumerate(tree_string):
        if char == '(':
            open_count += 1
            current_depth += 1
            max_depth = max(max_depth, current_depth)
        elif char == ')':
            close_count += 1
            current_depth -= 1
        
        # 检查是否出现负深度（关闭括号多于开启括号）
        if current_depth < 0:
            return {
                'balanced': False,
                'error_position': i,
                'open_count': open_count,
                'close_count': close_count,
                'max_depth': max_depth,
                'error': f"多余的关闭括号在位置 {i}"
            }
    
    return {
        'balanced': current_depth == 0,
        'open_count': open_count,
        'close_count': close_count,
        'max_depth': max_depth,
        'final_depth': current_depth,
        'error': None if current_depth == 0 else f"括号不匹配: 开括号{open_count}, 关闭括号{close_count}"
    }

def fix_newick_format(tree_string):
    """
    修复Newick格式的树字符串
    """
    # 移除首尾空白字符
    tree_string = tree_string.strip()
    
    # 确保以分号结尾
    if not tree_string.endswith(';'):
        tree_string += ';'
    
    # 分析当前结构
    analysis = analyze_tree_structure(tree_string.rstrip(';'))
    
    print(f"原始树分析:")
    print(f"  开括号数: {analysis['open_count']}")
    print(f"  关闭括号数: {analysis['close_count']}")
    print(f"  最大嵌套深度: {analysis['max_depth']}")
    print(f"  括号平衡: {analysis['balanced']}")
    if analysis['error']:
        print(f"  错误: {analysis['error']}")
    
    # 如果括号不平衡，尝试修复
    if not analysis['balanced']:
        # 计算需要添加或移除的括号数
        diff = analysis['open_count'] - analysis['close_count']
        
        if diff > 0:
            # 开括号多，需要添加关闭括号
            print(f"\n修复: 添加 {diff} 个关闭括号")
            tree_string = tree_string.rstrip(';') + ')' * diff + ';'
        elif diff < 0:
            # 关闭括号多，需要移除多余的关闭括号
            print(f"\n修复: 移除 {-diff} 个多余的关闭括号")
            # 从末尾移除多余的关闭括号
            tree_without_semicolon = tree_string.rstrip(';')
            for _ in range(-diff):
                last_close = tree_without_semicolon.rfind(')')
                if last_close != -1:
                    tree_without_semicolon = tree_without_semicolon[:last_close] + tree_without_semicolon[last_close+1:]
            tree_string = tree_without_semicolon + ';'
    
    # 检查是否有冗余的双重括号
    # 查找模式如 ((species)) 这样的冗余括号
    original_length = len(tree_string)
    
    # 移除开头的多余括号
    while tree_string.startswith('((') and not tree_string.startswith('((('):
        # 检查是否是真正的冗余括号
        temp_tree = tree_string[1:]  # 移除第一个括号
        temp_analysis = analyze_tree_structure(temp_tree.rstrip(';'))
        if temp_analysis['balanced']:
            tree_string = temp_tree
            print("移除了开头的冗余括号")
        else:
            break
    
    # 最终验证
    final_analysis = analyze_tree_structure(tree_string.rstrip(';'))
    
    print(f"\n修复后树分析:")
    print(f"  开括号数: {final_analysis['open_count']}")
    print(f"  关闭括号数: {final_analysis['close_count']}")
    print(f"  括号平衡: {final_analysis['balanced']}")
    print(f"  长度变化: {len(tree_string) - original_length}")
    
    return tree_string, final_analysis['balanced']

def create_simple_tree_from_species_list(tree_string):
    """
    从复杂的树中提取物种列表，创建简单的星形树
    """
    # 提取所有物种名称
    species_pattern = r'([A-Za-z][A-Za-z_]+[a-z])'
    species_matches = re.findall(species_pattern, tree_string)
    
    # 去重并排序
    unique_species = sorted(list(set(species_matches)))
    
    print(f"\n提取到 {len(unique_species)} 个物种")
    print("前10个物种:", unique_species[:10])
    
    # 创建简单的星形树
    if len(unique_species) > 1:
        simple_tree = '(' + ','.join(unique_species) + ');'
        return simple_tree, unique_species
    else:
        return None, unique_species

def main():
    """主函数"""
    input_file = "ANA_species_tree.tre"
    output_file = "ANA_species_tree_fixed.tre"
    simple_output_file = "ANA_species_tree_simple.tre"
    
    print("🔧 Newick树格式修复工具")
    print("=" * 60)
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在！")
        return False
    
    try:
        # 读取原始树文件
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        print(f"\n📊 原始文件信息:")
        print(f"  文件大小: {len(original_tree)} 字符")
        print(f"  前100个字符: {original_tree[:100]}...")
        
        # 修复树格式
        print(f"\n🔧 开始修复树格式...")
        fixed_tree, is_balanced = fix_newick_format(original_tree)
        
        if is_balanced:
            # 保存修复后的树
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(fixed_tree)
            
            print(f"\n✅ 修复成功!")
            print(f"📁 修复后的树已保存为: {output_file}")
            
            # 验证修复后的树
            try:
                from Bio import Phylo
                import io
                
                tree_io = io.StringIO(fixed_tree)
                tree = Phylo.read(tree_io, "newick")
                terminal_count = len(tree.get_terminals())
                print(f"🧬 树验证成功: 包含 {terminal_count} 个末端节点")
                
            except ImportError:
                print("📝 提示: 安装BioPython可以进行更详细的树验证")
            except Exception as e:
                print(f"⚠️ 树验证警告: {str(e)}")
        
        else:
            print(f"\n⚠️ 自动修复失败，尝试创建简单树...")
            
        # 创建简单的星形树作为备选方案
        simple_tree, species_list = create_simple_tree_from_species_list(original_tree)
        
        if simple_tree:
            with open(simple_output_file, 'w', encoding='utf-8') as f:
                f.write(simple_tree)
            
            print(f"📁 简单星形树已保存为: {simple_output_file}")
            print(f"🌟 包含 {len(species_list)} 个物种的星形树")
        
        print(f"\n💡 使用建议:")
        if is_balanced:
            print(f"  • 优先使用: {output_file} (修复后的原始树结构)")
        print(f"  • 备选方案: {simple_output_file} (简单星形树)")
        print(f"  • 在IQ-TREE中使用 -t 参数指定树文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 处理完成!")
        else:
            print(f"\n⚠️ 处理失败")
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
