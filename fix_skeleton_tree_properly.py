#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确修复骨架树格式，保持原始系统发育关系
只解决IQ-TREE的格式问题，不改变拓扑结构
"""

import re
import os

def analyze_tree_structure_detailed(tree_string):
    """
    详细分析树结构，找出具体的格式问题
    """
    print("详细分析树结构...")
    
    # 检查开头的括号模式
    opening_brackets = ""
    for i, char in enumerate(tree_string):
        if char == '(':
            opening_brackets += char
        else:
            break
    
    print(f"开头连续括号: '{opening_brackets}' (共{len(opening_brackets)}个)")
    
    # 检查是否有问题的模式
    problematic_patterns = [
        r'\(\(',  # 连续开括号
        r'\)\)',  # 连续关括号
        r'\(\)',  # 空括号
    ]
    
    issues = []
    for pattern in problematic_patterns:
        matches = list(re.finditer(pattern, tree_string))
        if matches:
            issues.append(f"发现 {len(matches)} 个 '{pattern}' 模式")
            for match in matches[:3]:  # 只显示前3个
                start, end = match.span()
                context = tree_string[max(0, start-10):end+10]
                issues.append(f"  位置 {start}: ...{context}...")
    
    return opening_brackets, issues

def fix_redundant_brackets_carefully(tree_string):
    """
    小心地修复冗余括号，保持树的拓扑结构
    """
    original_tree = tree_string.strip()
    
    # 确保以分号结尾
    if not original_tree.endswith(';'):
        original_tree += ';'
    
    print(f"原始树长度: {len(original_tree)}")
    
    # 分析问题
    opening_brackets, issues = analyze_tree_structure_detailed(original_tree)
    
    if issues:
        print("发现的问题:")
        for issue in issues:
            print(f"  {issue}")
    
    # 尝试修复策略1: 移除最外层的冗余括号
    fixed_tree = original_tree
    
    # 检查是否整个树被不必要的括号包围
    if fixed_tree.startswith('(') and fixed_tree.endswith(');'):
        # 计算括号深度，看是否可以安全移除最外层
        content = fixed_tree[1:-2]  # 移除最外层的 ( 和 );
        
        # 检查内容的括号平衡
        depth = 0
        min_depth = 0
        for char in content:
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            min_depth = min(min_depth, depth)
        
        # 如果最小深度不为负，且最终深度为0，说明可以安全移除最外层括号
        if min_depth >= 0 and depth == 0:
            print("尝试移除最外层括号...")
            fixed_tree = content + ';'
            print(f"移除后长度: {len(fixed_tree)}")
    
    # 尝试修复策略2: 处理连续的开括号
    if len(opening_brackets) > 2:
        print(f"检测到{len(opening_brackets)}个连续开括号，尝试优化...")
        
        # 找到第一个非括号字符的位置
        first_content_pos = 0
        for i, char in enumerate(fixed_tree):
            if char not in '()':
                first_content_pos = i
                break
        
        # 分析从第一个内容开始的结构
        if first_content_pos > 0:
            before_content = fixed_tree[:first_content_pos]
            from_content = fixed_tree[first_content_pos:]
            
            print(f"内容前的括号: '{before_content}'")
            
            # 如果开头有过多括号，尝试减少
            if len(before_content) > 3:  # 超过3个开括号可能有问题
                # 保留合理数量的开括号
                reasonable_brackets = '(' * min(3, len(before_content))
                fixed_tree = reasonable_brackets + from_content
                print(f"减少开括号后: {len(fixed_tree)} 字符")
    
    # 验证修复后的树
    open_count = fixed_tree.count('(')
    close_count = fixed_tree.count(')')
    
    print(f"修复后统计:")
    print(f"  开括号: {open_count}")
    print(f"  关括号: {close_count}")
    print(f"  平衡: {'是' if open_count == close_count else '否'}")
    
    # 如果不平衡，尝试修复
    if open_count != close_count:
        diff = open_count - close_count
        if diff > 0:
            # 需要添加关括号
            fixed_tree = fixed_tree.rstrip(';') + ')' * diff + ';'
            print(f"添加了 {diff} 个关括号")
        elif diff < 0:
            # 需要移除关括号
            for _ in range(-diff):
                last_close = fixed_tree.rfind(')')
                if last_close != -1:
                    fixed_tree = fixed_tree[:last_close] + fixed_tree[last_close+1:]
            print(f"移除了 {-diff} 个关括号")
    
    return fixed_tree

def create_alternative_skeleton_tree(original_tree):
    """
    创建替代的骨架树格式，保持尽可能多的原始结构
    """
    # 提取所有物种名称
    species_pattern = r'\b([A-Za-z][A-Za-z0-9_-]*_[A-Za-z0-9_-]*)\b'
    species_list = list(set(re.findall(species_pattern, original_tree)))
    species_list.sort()
    
    print(f"提取到 {len(species_list)} 个物种")
    
    # 尝试创建一个简化但仍保持一些结构的树
    # 将物种分组，模拟原始的层次结构
    
    if len(species_list) <= 10:
        # 小数量，创建简单结构
        return f"({','.join(species_list)});"
    
    # 大数量，创建分层结构
    group_size = max(10, len(species_list) // 8)  # 每组最多10个物种
    groups = []
    
    for i in range(0, len(species_list), group_size):
        group = species_list[i:i+group_size]
        if len(group) == 1:
            groups.append(group[0])
        else:
            groups.append(f"({','.join(group)})")
    
    # 创建分层结构
    if len(groups) <= 4:
        return f"({','.join(groups)});"
    else:
        # 进一步分组
        mid = len(groups) // 2
        left_groups = groups[:mid]
        right_groups = groups[mid:]
        
        left_part = f"({','.join(left_groups)})" if len(left_groups) > 1 else left_groups[0]
        right_part = f"({','.join(right_groups)})" if len(right_groups) > 1 else right_groups[0]
        
        return f"({left_part},{right_part});"

def main():
    """主函数"""
    input_file = "ANA_species_tree.tre"  # 使用原始文件
    
    print("🔧 正确修复骨架树格式")
    print("目标: 保持原始系统发育关系，只解决IQ-TREE格式问题")
    print("=" * 70)
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        print(f"📊 原始文件信息:")
        print(f"  长度: {len(original_tree)} 字符")
        
        # 方法1: 小心修复原始树结构
        print(f"\n🔧 方法1: 修复原始树结构...")
        fixed_tree = fix_redundant_brackets_carefully(original_tree)
        
        # 保存修复后的树
        output_file1 = "ANA_skeleton_tree_fixed.tre"
        with open(output_file1, 'w', encoding='utf-8') as f:
            f.write(fixed_tree)
        
        print(f"✅ 保存修复后的骨架树: {output_file1}")
        
        # 方法2: 创建结构化的替代树
        print(f"\n🔧 方法2: 创建结构化替代树...")
        alternative_tree = create_alternative_skeleton_tree(original_tree)
        
        output_file2 = "ANA_skeleton_tree_structured.tre"
        with open(output_file2, 'w', encoding='utf-8') as f:
            f.write(alternative_tree)
        
        print(f"✅ 保存结构化替代树: {output_file2}")
        
        # 验证两个文件
        print(f"\n📊 文件对比:")
        print(f"  原始树: {len(original_tree)} 字符")
        print(f"  修复树: {len(fixed_tree)} 字符")
        print(f"  替代树: {len(alternative_tree)} 字符")
        
        print(f"\n💡 使用建议:")
        print(f"1. 首先尝试: {output_file1} (保持最多原始结构)")
        print(f"2. 如果失败，尝试: {output_file2} (简化但保持层次)")
        print(f"3. 如果都失败，考虑不使用约束树")
        
        print(f"\n📝 IQ-TREE命令:")
        print(f"iqtree2 -s concat.phy -p ANA_concat.txt -t {output_file1} -m MFP -bb 1000")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 骨架树修复完成!")
            print(f"现在您有了保持原始系统发育关系的修复版本")
        else:
            print(f"\n❌ 修复失败")
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
