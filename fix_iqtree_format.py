#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门为IQ-TREE修复Newick格式问题
解决 "Redundant double-bracket" 错误
"""

import re
import os

def analyze_bracket_pattern(tree_string):
    """
    分析括号模式，找出可能的冗余括号
    """
    print("分析括号模式...")
    
    # 查看开头的括号模式
    start_pattern = ""
    for i, char in enumerate(tree_string[:50]):
        if char in "()":
            start_pattern += char
        else:
            break
    
    print(f"开头括号模式: {start_pattern}")
    
    # 统计连续开括号
    consecutive_open = 0
    max_consecutive = 0
    for char in tree_string:
        if char == '(':
            consecutive_open += 1
            max_consecutive = max(max_consecutive, consecutive_open)
        else:
            consecutive_open = 0
    
    print(f"最大连续开括号数: {max_consecutive}")
    
    return start_pattern, max_consecutive

def create_minimal_valid_tree(species_list):
    """
    创建最简单的有效Newick树
    """
    if len(species_list) == 1:
        return f"{species_list[0]};"
    elif len(species_list) == 2:
        return f"({species_list[0]},{species_list[1]});"
    else:
        # 创建简单的二分树结构
        if len(species_list) <= 4:
            # 小数量直接创建星形树
            return f"({','.join(species_list)});"
        else:
            # 大数量创建平衡二分树
            mid = len(species_list) // 2
            left_group = species_list[:mid]
            right_group = species_list[mid:]
            
            if len(left_group) == 1:
                left_part = left_group[0]
            else:
                left_part = f"({','.join(left_group)})"
            
            if len(right_group) == 1:
                right_part = right_group[0]
            else:
                right_part = f"({','.join(right_group)})"
            
            return f"({left_part},{right_part});"

def extract_species_from_tree(tree_string):
    """
    从树字符串中提取所有物种名称
    """
    # 使用正则表达式提取物种名称
    # 物种名称通常是字母开头，包含字母、数字、下划线和连字符
    species_pattern = r'\b([A-Za-z][A-Za-z0-9_-]*[a-z])\b'
    species_matches = re.findall(species_pattern, tree_string)
    
    # 过滤掉可能的非物种名称
    filtered_species = []
    for species in species_matches:
        # 过滤条件：长度大于2，包含下划线（通常物种名格式为Genus_species）
        if len(species) > 2 and '_' in species:
            filtered_species.append(species)
    
    # 去重并排序
    unique_species = sorted(list(set(filtered_species)))
    
    return unique_species

def create_iqtree_compatible_trees(input_file):
    """
    创建多种IQ-TREE兼容的树格式
    """
    print(f"🔧 为IQ-TREE创建兼容的树格式")
    print("=" * 60)
    
    # 读取原始树
    with open(input_file, 'r', encoding='utf-8') as f:
        original_tree = f.read().strip()
    
    print(f"原始树长度: {len(original_tree)} 字符")
    
    # 分析括号模式
    start_pattern, max_consecutive = analyze_bracket_pattern(original_tree)
    
    # 提取物种列表
    species_list = extract_species_from_tree(original_tree)
    print(f"提取到 {len(species_list)} 个物种")
    
    if len(species_list) == 0:
        print("❌ 错误: 未能提取到物种名称")
        return False
    
    # 显示前10个物种
    print("前10个物种:", species_list[:10])
    
    # 创建多种格式的树
    trees_to_create = [
        ("simple_star", f"({','.join(species_list)});"),
        ("minimal_binary", create_minimal_valid_tree(species_list)),
    ]
    
    # 如果原始树的连续括号不超过3个，尝试简化
    if max_consecutive <= 3:
        # 尝试移除最外层括号
        simplified = original_tree.strip()
        if simplified.startswith('(') and simplified.endswith(');'):
            # 检查是否可以安全移除最外层括号
            inner_content = simplified[1:-2]  # 移除最外层的 ( 和 );
            if inner_content.count('(') == inner_content.count(')'):
                trees_to_create.append(("simplified_original", inner_content + ";"))
    
    # 创建超简单版本（只有几个代表物种）
    if len(species_list) > 10:
        representative_species = species_list[:10]  # 取前10个物种
        trees_to_create.append(("representative", f"({','.join(representative_species)});"))
    
    # 保存所有版本
    success_count = 0
    for tree_name, tree_content in trees_to_create:
        output_file = f"ANA_tree_{tree_name}.tre"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(tree_content)
            
            print(f"✅ 创建: {output_file}")
            print(f"   长度: {len(tree_content)} 字符")
            print(f"   物种数: {tree_content.count(',') + 1}")
            
            # 验证括号
            open_count = tree_content.count('(')
            close_count = tree_content.count(')')
            print(f"   括号: {open_count} 开, {close_count} 关")
            
            if open_count == close_count:
                print(f"   ✅ 括号平衡")
                success_count += 1
            else:
                print(f"   ❌ 括号不平衡")
            
            print()
            
        except Exception as e:
            print(f"❌ 创建 {output_file} 失败: {str(e)}")
    
    return success_count > 0

def main():
    """主函数"""
    input_file = "ANA_species_tree_fixed.tre"
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    try:
        success = create_iqtree_compatible_trees(input_file)
        
        if success:
            print("🎉 成功创建IQ-TREE兼容的树文件!")
            print("\n💡 使用建议:")
            print("1. 首先尝试: ANA_tree_simple_star.tre")
            print("2. 如果失败，尝试: ANA_tree_minimal_binary.tre")
            print("3. 如果还失败，尝试: ANA_tree_representative.tre")
            print("\n📝 IQ-TREE命令示例:")
            print("iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_tree_simple_star.tre -m MFP -bb 1000")
            print("\n或者不使用约束树:")
            print("iqtree2 -s concat.phy -p ANA_concat.txt -m MFP -bb 1000")
        else:
            print("❌ 创建树文件失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
