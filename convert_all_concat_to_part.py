#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量将concat.cfg文件格式转换为类似Fabales.part的格式
处理: dicots_concat.cfg, monocot_concat.cfg, superasterids_concat.cfg, superrosids_concat.cfg
"""

import re
import os

def parse_concat_cfg(filename):
    """
    解析concat.cfg文件，提取基因信息
    """
    gene_ranges = {}
    
    if not os.path.exists(filename):
        print(f"❌ 文件 {filename} 不存在，跳过")
        return gene_ranges
    
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找[data_blocks]部分
    in_data_blocks = False
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行和注释
        if not line or line.startswith('#') or line.startswith('##'):
            continue
            
        # 检查是否进入data_blocks部分
        if line == '[data_blocks]':
            in_data_blocks = True
            continue
        
        # 检查是否离开data_blocks部分
        if line.startswith('[') and line != '[data_blocks]':
            in_data_blocks = False
            continue
        
        # 解析data_blocks中的行
        if in_data_blocks and '=' in line:
            # 解析格式: msa_18S_pos1 = 1-1809\3;
            parts = line.split('=')
            if len(parts) == 2:
                gene_info = parts[0].strip()
                range_info = parts[1].strip().rstrip(';')
                
                # 提取基因名称（移除msa_前缀和_pos后缀）
                if gene_info.startswith('msa_') and '_pos' in gene_info:
                    gene_name = gene_info.replace('msa_', '').split('_pos')[0]
                    
                    # 解析范围信息
                    if '\\3' in range_info:
                        # 格式: 1-1809\3 表示从1到1809，步长为3
                        range_part = range_info.split('\\')[0]
                        if '-' in range_part:
                            start, end = range_part.split('-')
                            start = int(start.strip())
                            end = int(end.strip())
                            
                            # 存储基因的完整范围（不考虑密码子位置）
                            if gene_name not in gene_ranges:
                                gene_ranges[gene_name] = {'start': start, 'end': end}
                            else:
                                # 更新范围以包含所有位置
                                gene_ranges[gene_name]['start'] = min(gene_ranges[gene_name]['start'], start)
                                gene_ranges[gene_name]['end'] = max(gene_ranges[gene_name]['end'], end)
    
    return gene_ranges

def write_part_format(gene_ranges, output_filename):
    """
    将基因范围信息写入part格式文件
    """
    if not gene_ranges:
        print(f"⚠️ 没有基因信息可写入 {output_filename}")
        return 0
    
    # 按起始位置排序
    sorted_genes = sorted(gene_ranges.items(), key=lambda x: x[1]['start'])
    
    with open(output_filename, 'w', encoding='utf-8') as f:
        for gene_name, range_info in sorted_genes:
            start = range_info['start']
            end = range_info['end']
            f.write(f"DNA, {gene_name} = {start}-{end}\n")
    
    return len(sorted_genes)

def process_single_file(classification):
    """
    处理单个分类的concat.cfg文件
    """
    input_file = f"{classification}_concat.cfg"
    output_file = f"{classification}_concat.txt"
    
    print(f"\n🔄 处理 {classification} 分类")
    print("=" * 50)
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    
    try:
        # 解析concat.cfg文件
        print(f"🔍 正在解析 {input_file}...")
        gene_ranges = parse_concat_cfg(input_file)
        
        if not gene_ranges:
            print(f"❌ 未找到有效的基因信息")
            return False
        
        print(f"✅ 成功解析 {len(gene_ranges)} 个基因")
        
        # 显示前几个基因作为预览
        print(f"📋 基因信息预览（前5个）:")
        sorted_genes = sorted(gene_ranges.items(), key=lambda x: x[1]['start'])
        for i, (gene_name, range_info) in enumerate(sorted_genes[:5], 1):
            start = range_info['start']
            end = range_info['end']
            length = end - start + 1
            print(f"   {i:2d}. {gene_name:15} = {start:6d}-{end:6d} (长度: {length:5d})")
        
        if len(gene_ranges) > 5:
            print(f"   ... 还有 {len(gene_ranges) - 5} 个基因")
        
        # 写入part格式文件
        print(f"💾 正在写入 {output_file}...")
        gene_count = write_part_format(gene_ranges, output_file)
        
        if gene_count > 0:
            print(f"✅ 转换完成!")
            print(f"📊 统计信息:")
            print(f"   • 处理的基因数: {gene_count}")
            print(f"   • 输出文件: {os.path.abspath(output_file)}")
            
            # 计算总长度
            total_length = max(range_info['end'] for range_info in gene_ranges.values())
            print(f"   • 序列总长度: {total_length}")
            
            return True
        else:
            print(f"❌ 写入失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理 {classification} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 要处理的分类列表
    classifications = ['dicots', 'monocot', 'superasterids', 'superrosids']
    
    print("🔄 批量转换concat.cfg到part格式")
    print("目标: 将concat.cfg格式转换为类似Fabales.part的格式")
    print("=" * 70)
    
    success_count = 0
    total_genes = 0
    
    for classification in classifications:
        if process_single_file(classification):
            success_count += 1
            
            # 统计基因数
            output_file = f"{classification}_concat.txt"
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    gene_count = len(f.readlines())
                    total_genes += gene_count
    
    # 输出最终统计结果
    print(f"\n" + "=" * 70)
    print("🎉 批量转换完成！最终统计:")
    print("=" * 70)
    print(f"✅ 成功处理: {success_count}/{len(classifications)} 个分类")
    print(f"🧬 总基因数: {total_genes}")
    
    if success_count > 0:
        print(f"\n📋 生成的文件列表:")
        for classification in classifications:
            output_file = f"{classification}_concat.txt"
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                print(f"   • {output_file} ({lines} 个基因)")
        
        print(f"\n📝 IQ-TREE使用示例:")
        for classification in classifications:
            output_file = f"{classification}_concat.txt"
            if os.path.exists(output_file):
                print(f"iqtree2 -s concat.phy -p {output_file} -m MFP -bb 1000")
    
    print(f"\n🎯 转换完成!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
