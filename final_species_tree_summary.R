# 骨架树扩展为物种树的最终总结

library(ape)

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

cat(paste(rep("=", 80), collapse = ""), "\n")
cat("骨架树扩展为物种树 - 最终处理结果总结\n")
cat(paste(rep("=", 80), collapse = ""), "\n\n")

# 分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

cat("1. 处理流程总结:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("步骤1: 从原始系统发育树中提取各分类的骨架树 (使用keep.tip())\n")
cat("步骤2: 将骨架树中的科名替换为该科下的所有物种名称\n")
cat("步骤3: 生成包含物种级别信息的完整系统发育树\n\n")

cat("2. 生成的文件类型:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")

total_skeleton_size <- 0
total_species_size <- 0
total_species_count <- 0

for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  skeleton_file <- paste0(class_name, "_subtree.tre")
  species_file <- paste0(class_name, "_species_tree.tre")
  
  cat(sprintf("\n%s 分类:\n", class_name))
  
  # 读取分类数据统计
  if (file.exists(class_file)) {
    class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
    species_count <- nrow(class_data)
    family_count <- length(unique(class_data$wcvp_family))
    
    cat(sprintf("  • 原始数据: %d 个物种, %d 个科\n", species_count, family_count))
    total_species_count <- total_species_count + species_count
  }
  
  # 骨架树信息
  if (file.exists(skeleton_file)) {
    skeleton_tree <- read.tree(skeleton_file)
    skeleton_content <- readLines(skeleton_file)
    skeleton_size <- nchar(skeleton_content[1])
    
    cat(sprintf("  • 骨架树: %s (%d 个科, %d 字符)\n", 
                skeleton_file, length(skeleton_tree$tip.label), skeleton_size))
    total_skeleton_size <- total_skeleton_size + skeleton_size
  }
  
  # 物种树信息
  if (file.exists(species_file)) {
    species_content <- readLines(species_file)
    species_size <- nchar(species_content[1])
    
    cat(sprintf("  • 物种树: %s (%d 字符)\n", species_file, species_size))
    cat(sprintf("  • 扩展倍数: %.1f 倍\n", species_size / skeleton_size))
    total_species_size <- total_species_size + species_size
  }
}

cat(sprintf("\n总计统计:\n"))
cat(sprintf("  • 总物种数: %d\n", total_species_count))
cat(sprintf("  • 骨架树总大小: %d 字符\n", total_skeleton_size))
cat(sprintf("  • 物种树总大小: %d 字符\n", total_species_size))
cat(sprintf("  • 平均扩展倍数: %.1f 倍\n", total_species_size / total_skeleton_size))

cat("\n3. 物种树格式说明:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("• 保持原始骨架树的系统发育结构\n")
cat("• 科名被替换为 (物种1,物种2,物种3,...) 格式\n")
cat("• 物种名称用逗号分隔\n")
cat("• 每个科的物种组用括号包围\n")
cat("• 标准Newick格式，兼容系统发育分析软件\n\n")

cat("4. 示例格式:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("原始骨架树: ((FamilyA,FamilyB),FamilyC);\n")
cat("扩展物种树: (((Species1,Species2),(Species3,Species4,Species5)),(Species6));\n\n")

cat("5. 应用场景:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("• 物种级别的系统发育分析\n")
cat("• 群落生态学研究\n")
cat("• 系统发育多样性计算\n")
cat("• 进化生物学研究\n")
cat("• 比较系统发育学分析\n\n")

cat("6. 技术特点:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("• 使用R语言ape包的keep.tip()函数提取骨架树\n")
cat("• 保持原始系统发育关系不变\n")
cat("• 自动处理物种名称格式化\n")
cat("• 支持大规模数据处理\n")
cat("• 生成标准格式的系统发育树文件\n\n")

# 验证一个小例子
cat("7. ANA分类示例验证:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
if (file.exists("ANA_species_tree.tre")) {
  ana_content <- readLines("ANA_species_tree.tre")
  ana_tree_string <- ana_content[1]
  
  # 显示树的开头部分
  if (nchar(ana_tree_string) > 150) {
    cat("ANA物种树开头部分:\n")
    cat(substr(ana_tree_string, 1, 150), "...\n\n")
  }
  
  # 统计括号对数（科数）
  open_paren_count <- length(gregexpr("\\(", ana_tree_string)[[1]])
  comma_count <- length(gregexpr(",", ana_tree_string)[[1]])
  
  cat(sprintf("• 包含 %d 个科（括号对）\n", open_paren_count))
  cat(sprintf("• 包含 %d 个物种分隔符（逗号）\n", comma_count))
  cat(sprintf("• 估计物种数: %d\n", comma_count + 1))
}

cat("\n", paste(rep("=", 80), collapse = ""), "\n")
cat("骨架树扩展完成！所有物种树文件已成功生成。\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
