#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的括号修复方案
专门解决IQ-TREE的"冗余双重括号"问题，保持括号平衡
"""

import re
import os

def fix_redundant_double_brackets(tree_string):
    """
    修复冗余双重括号，保持括号平衡和拓扑结构
    """
    print("修复冗余双重括号...")
    
    original_tree = tree_string.strip()
    if not original_tree.endswith(';'):
        original_tree += ';'
    
    fixed_tree = original_tree
    
    # IQ-TREE的"冗余双重括号"错误通常指的是这些模式:
    # 1. ((species)) - 单个物种被双重括号包围
    # 2. ))),( - 连续的关括号后跟开括号
    # 3. 过度嵌套的括号结构
    
    # 修复模式1: ((单个物种名))
    pattern1 = r'\(\(([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\)\)'
    matches1 = re.findall(pattern1, fixed_tree)
    if matches1:
        print(f"  修复 {len(matches1)} 个双重包围的单个物种")
        fixed_tree = re.sub(pattern1, r'(\1)', fixed_tree)
    
    # 修复模式2: 连续的关括号模式 - 更保守的方法
    # 将 ))),( 替换为 )),(，但要确保不破坏结构
    problematic_positions = []
    for match in re.finditer(r'\)\)\),\(', fixed_tree):
        start, end = match.span()
        problematic_positions.append((start, end))
    
    if problematic_positions:
        print(f"  发现 {len(problematic_positions)} 处 ))),( 模式")
        # 从后往前替换，避免位置偏移
        for start, end in reversed(problematic_positions):
            # 检查这个位置的上下文
            context_start = max(0, start - 20)
            context_end = min(len(fixed_tree), end + 20)
            context = fixed_tree[context_start:context_end]
            
            # 只有在确认安全的情况下才替换
            # 检查前面是否有足够的开括号来匹配
            before_part = fixed_tree[:start]
            open_count = before_part.count('(')
            close_count = before_part.count(')')
            
            if open_count > close_count + 2:  # 有足够的开括号
                # 将 ))),( 替换为 )),(
                fixed_tree = fixed_tree[:start] + ')),((' + fixed_tree[end:]
                print(f"    位置 {start}: 修复 ))),( -> )),(")
    
    # 验证修复结果
    orig_open = original_tree.count('(')
    orig_close = original_tree.count(')')
    fixed_open = fixed_tree.count('(')
    fixed_close = fixed_tree.count(')')
    
    print(f"  原始括号: {orig_open} 开, {orig_close} 关")
    print(f"  修复后括号: {fixed_open} 开, {fixed_close} 关")
    
    # 如果括号不平衡，回退到原始版本
    if fixed_open != fixed_close:
        print("  ⚠️ 修复导致括号不平衡，回退到原始版本")
        return original_tree
    
    # 验证物种数量
    species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
    orig_species = set(re.findall(species_pattern, original_tree))
    fixed_species = set(re.findall(species_pattern, fixed_tree))
    
    if len(orig_species) != len(fixed_species):
        print("  ⚠️ 修复导致物种数量变化，回退到原始版本")
        return original_tree
    
    print(f"  ✅ 修复成功，物种数量保持: {len(fixed_species)}")
    return fixed_tree

def create_safe_skeleton_tree(input_file, output_file):
    """
    创建安全的骨架树版本
    """
    print(f"创建安全骨架树: {input_file} -> {output_file}")
    print("-" * 50)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        print(f"原始文件大小: {len(original_tree)} 字符")
        
        # 应用修复
        fixed_tree = fix_redundant_double_brackets(original_tree)
        
        # 最终验证
        open_count = fixed_tree.count('(')
        close_count = fixed_tree.count(')')
        
        if open_count == close_count:
            # 保存修复后的文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(fixed_tree)
            
            print(f"✅ 安全骨架树创建完成: {output_file}")
            return True
        else:
            print(f"❌ 括号不平衡: {open_count} 开, {close_count} 关")
            return False
            
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        return False

def create_simplified_skeleton_tree(input_file, output_file):
    """
    创建简化但保持结构的骨架树
    """
    print(f"创建简化骨架树: {input_file} -> {output_file}")
    print("-" * 50)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 提取所有物种，保持原始顺序
        species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
        all_species = re.findall(species_pattern, original_tree)
        unique_species = []
        seen = set()
        for species in all_species:
            if species not in seen:
                unique_species.append(species)
                seen.add(species)
        
        print(f"提取到 {len(unique_species)} 个物种")
        
        # 创建简化的三分组结构，保持一些层次
        if len(unique_species) <= 3:
            simplified_tree = f"({','.join(unique_species)});"
        else:
            # 三分组
            third = len(unique_species) // 3
            group1 = unique_species[:third]
            group2 = unique_species[third:2*third]
            group3 = unique_species[2*third:]
            
            # 创建有层次的结构
            if len(group1) == 1:
                g1_str = group1[0]
            else:
                g1_str = f"({','.join(group1)})"
            
            if len(group2) == 1:
                g2_str = group2[0]
            else:
                g2_str = f"({','.join(group2)})"
            
            if len(group3) == 1:
                g3_str = group3[0]
            else:
                g3_str = f"({','.join(group3)})"
            
            simplified_tree = f"(({g1_str},{g2_str}),{g3_str});"
        
        # 验证新树
        new_open = simplified_tree.count('(')
        new_close = simplified_tree.count(')')
        
        print(f"简化树括号: {new_open} 开, {new_close} 关")
        print(f"简化树长度: {len(simplified_tree)} 字符")
        
        if new_open == new_close:
            # 保存
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(simplified_tree)
            
            print(f"✅ 简化骨架树创建完成: {output_file}")
            return True
        else:
            print(f"❌ 简化树括号不平衡")
            return False
        
    except Exception as e:
        print(f"❌ 简化骨架树创建失败: {str(e)}")
        return False

def main():
    """主函数"""
    tree_files = [
        "superrosids_species_tree.tre",
        "ANA_species_tree.tre", 
        "dicots_species_tree.tre",
        "monocot_species_tree.tre",
        "superasterids_species_tree.tre"
    ]
    
    print("🔧 最终骨架树修复方案")
    print("目标: 保持拓扑结构，确保IQ-TREE兼容性")
    print("=" * 80)
    
    for tree_file in tree_files:
        if os.path.exists(tree_file):
            base_name = tree_file.replace('_species_tree.tre', '')
            
            print(f"\n处理: {tree_file}")
            
            # 方案1: 安全修复（保持原始结构）
            safe_output = f"{base_name}_SAFE.tre"
            success1 = create_safe_skeleton_tree(tree_file, safe_output)
            
            # 方案2: 简化骨架树（保持层次但简化）
            simple_output = f"{base_name}_SIMPLE.tre"
            success2 = create_simplified_skeleton_tree(tree_file, simple_output)
            
            if success1 or success2:
                print(f"\n💡 推荐使用顺序:")
                if success1:
                    print(f"1. iqtree2 -s concat.phy -p {base_name}_concat.txt -t {safe_output} -m MFP -bb 1000")
                if success2:
                    print(f"2. iqtree2 -s concat.phy -p {base_name}_concat.txt -t {simple_output} -m MFP -bb 1000")
                print(f"3. iqtree2 -s concat.phy -p {base_name}_concat.txt -m MFP -bb 1000  # 无约束")
        else:
            print(f"⚠️ 文件不存在: {tree_file}")
    
    print(f"\n🎯 总结:")
    print(f"- *_SAFE.tre: 保持原始拓扑，最小修复")
    print(f"- *_SIMPLE.tre: 简化结构，保持层次")
    print(f"- 无约束: 最后的选择")

if __name__ == "__main__":
    main()
