#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确修复species_tree.tre文件的冗余括号问题
保持拓扑结构，只移除最外层的冗余包装
"""

import os
import re

def remove_redundant_outer_brackets(tree_string):
    """
    智能移除冗余的最外层括号，保持拓扑结构
    """
    original_tree = tree_string.strip()
    
    # 确保以分号结尾
    if not original_tree.endswith(';'):
        original_tree += ';'
    
    print(f"原始树长度: {len(original_tree)}")
    
    # 分析开头的连续括号
    opening_brackets = 0
    for char in original_tree:
        if char == '(':
            opening_brackets += 1
        else:
            break
    
    print(f"开头连续括号数: {opening_brackets}")
    
    if opening_brackets <= 2:
        print("括号数量正常，无需修复")
        return original_tree
    
    # 尝试移除冗余的最外层括号
    fixed_tree = original_tree
    removed_layers = 0
    
    while fixed_tree.startswith('(') and fixed_tree.endswith(');'):
        # 检查是否可以安全移除最外层括号
        inner_content = fixed_tree[1:-2]  # 移除最外层的 ( 和 );
        
        # 验证内容的括号平衡
        depth = 0
        min_depth = 0
        for char in inner_content:
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            min_depth = min(min_depth, depth)
        
        # 如果最小深度不为负且最终深度为0，说明可以安全移除最外层
        if min_depth >= 0 and depth == 0:
            # 计算顶层的逗号数（不在括号内的逗号）
            top_level_commas = 0
            depth = 0
            for char in inner_content:
                if char == '(':
                    depth += 1
                elif char == ')':
                    depth -= 1
                elif char == ',' and depth == 0:
                    top_level_commas += 1
            
            # 如果顶层只有一个分支（没有逗号），说明最外层括号是冗余的
            if top_level_commas == 0:
                fixed_tree = inner_content + ';'
                removed_layers += 1
                print(f"移除第 {removed_layers} 层冗余括号")
                
                # 检查新的开头括号数
                new_opening = 0
                for char in fixed_tree:
                    if char == '(':
                        new_opening += 1
                    else:
                        break
                
                print(f"移除后开头括号数: {new_opening}")
                
                # 如果还有过多括号，继续移除
                if new_opening <= 3:
                    break
            else:
                print(f"顶层有 {top_level_commas + 1} 个分支，保留最外层括号")
                break
        else:
            print("内容括号不平衡，停止移除")
            break
    
    print(f"总共移除了 {removed_layers} 层冗余括号")
    print(f"修复后树长度: {len(fixed_tree)}")
    
    return fixed_tree

def fix_species_tree(input_file, output_file):
    """
    修复单个species_tree文件
    """
    print(f"\n修复文件: {input_file} -> {output_file}")
    print("-" * 60)
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 修复冗余括号
        fixed_tree = remove_redundant_outer_brackets(original_tree)
        
        # 验证修复结果
        orig_open = original_tree.count('(')
        orig_close = original_tree.count(')')
        fixed_open = fixed_tree.count('(')
        fixed_close = fixed_tree.count(')')
        
        print(f"验证结果:")
        print(f"  原始括号: {orig_open} 开, {orig_close} 关")
        print(f"  修复后括号: {fixed_open} 开, {fixed_close} 关")
        print(f"  括号平衡: {'是' if fixed_open == fixed_close else '否'}")
        
        # 验证物种数量
        orig_species = len(set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', original_tree)))
        fixed_species = len(set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', fixed_tree)))
        
        print(f"  原始物种数: {orig_species}")
        print(f"  修复后物种数: {fixed_species}")
        print(f"  物种保持: {'是' if orig_species == fixed_species else '否'}")
        
        # 保存修复后的树
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_tree)
        
        print(f"✅ 修复完成: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    tree_files = [
        ("ANA_species_tree.tre", "ANA_species_tree_fixed.tre"),
        ("dicots_species_tree.tre", "dicots_species_tree_fixed.tre"),
        ("monocot_species_tree.tre", "monocot_species_tree_fixed.tre"),
        ("superrosids_species_tree.tre", "superrosids_species_tree_fixed.tre"),
        ("superasterids_species_tree.tre", "superasterids_species_tree_fixed.tre")
    ]
    
    print("🔧 正确修复species_tree.tre文件的冗余括号")
    print("目标: 保持拓扑结构，移除冗余的最外层括号")
    print("=" * 80)
    
    success_count = 0
    
    for input_file, output_file in tree_files:
        if os.path.exists(input_file):
            if fix_species_tree(input_file, output_file):
                success_count += 1
        else:
            print(f"\n⚠️ 文件不存在: {input_file}")
    
    print(f"\n" + "=" * 80)
    print(f"🎉 修复完成: {success_count}/{len(tree_files)} 个文件")
    
    if success_count > 0:
        print(f"\n💡 使用建议:")
        print(f"1. 优先使用 *_species_tree_fixed.tre 文件")
        print(f"2. 这些文件保持了原始的拓扑结构")
        print(f"3. 只移除了冗余的最外层括号包装")
        
        print(f"\n📝 IQ-TREE命令示例:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_species_tree_fixed.tre -m MFP -bb 1000")
        print(f"iqtree2 -s concat.phy -p ANA_concat.txt -t ANA_species_tree_fixed.tre -m MFP -bb 1000")
        
        print(f"\n🔍 如果仍有问题:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -m MFP -bb 1000  # 不使用约束树")
    
    return success_count > 0

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
