#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复大型骨架树的格式问题
解决IQ-TREE的"冗余双重括号"错误
"""

import re
import os

def create_ultra_simple_tree(species_list, tree_name):
    """
    创建超简单的树格式，避免任何可能的括号问题
    """
    print(f"为 {tree_name} 创建超简单树格式...")
    print(f"物种数: {len(species_list)}")
    
    if len(species_list) <= 1:
        return f"{species_list[0]};" if species_list else "();"
    
    # 对于大量物种，创建最简单的星形树
    # 格式: (species1,species2,species3,...,speciesN);
    tree_content = "(" + ",".join(species_list) + ");"
    
    # 验证括号
    open_count = tree_content.count('(')
    close_count = tree_content.count(')')
    
    print(f"  树长度: {len(tree_content)} 字符")
    print(f"  开括号: {open_count}, 关括号: {close_count}")
    print(f"  括号平衡: {'是' if open_count == close_count else '否'}")
    
    return tree_content

def extract_species_from_tree_file(filename):
    """
    从树文件中提取所有物种名称
    """
    print(f"从 {filename} 提取物种...")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 使用正则表达式提取物种名称
        # 物种名称格式: Genus_species
        species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
        species_matches = re.findall(species_pattern, content)
        
        # 去重并排序
        unique_species = sorted(list(set(species_matches)))
        
        print(f"  提取到 {len(unique_species)} 个唯一物种")
        
        return unique_species
        
    except Exception as e:
        print(f"  错误: {str(e)}")
        return []

def fix_all_large_trees():
    """
    修复所有大型骨架树
    """
    trees_to_fix = [
        ("monocot_taxonomic_skeleton_tree.tre", "monocot"),
        ("superrosids_taxonomic_skeleton_tree.tre", "superrosids"), 
        ("superasterids_taxonomic_skeleton_tree.tre", "superasterids"),
        ("dicots_taxonomic_skeleton_tree.tre", "dicots")
    ]
    
    print("🔧 修复大型骨架树格式问题")
    print("=" * 60)
    
    success_count = 0
    
    for tree_file, tree_name in trees_to_fix:
        print(f"\n处理 {tree_name}...")
        
        if not os.path.exists(tree_file):
            print(f"  ⚠️ 文件不存在: {tree_file}")
            continue
        
        try:
            # 提取物种列表
            species_list = extract_species_from_tree_file(tree_file)
            
            if not species_list:
                print(f"  ❌ 未能提取到物种")
                continue
            
            # 创建超简单树
            simple_tree = create_ultra_simple_tree(species_list, tree_name)
            
            # 保存修复后的树
            output_file = f"{tree_name}_simple_skeleton.tre"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(simple_tree)
            
            print(f"  ✅ 已保存: {output_file}")
            success_count += 1
            
            # 验证文件
            with open(output_file, 'r', encoding='utf-8') as f:
                verification = f.read().strip()
            
            v_open = verification.count('(')
            v_close = verification.count(')')
            v_species = verification.count(',') + 1
            
            print(f"  验证: {v_open} 开括号, {v_close} 关括号, {v_species} 物种")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
    
    return success_count

def create_test_trees():
    """
    创建测试用的小型树
    """
    print(f"\n🧪 创建测试树...")
    
    test_trees = {
        "test_small": ["Species_one", "Species_two", "Species_three"],
        "test_medium": [f"Species_{i:03d}" for i in range(1, 11)],
        "test_large": [f"Species_{i:04d}" for i in range(1, 101)]
    }
    
    for test_name, species_list in test_trees.items():
        tree_content = create_ultra_simple_tree(species_list, test_name)
        output_file = f"{test_name}_skeleton.tre"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(tree_content)
        
        print(f"  ✅ 创建测试树: {output_file} ({len(species_list)} 物种)")

def main():
    """主函数"""
    print("🌳 大型骨架树格式修复工具")
    print("目标: 创建IQ-TREE完全兼容的简单骨架树")
    print("=" * 70)
    
    try:
        # 修复现有的大型树
        success_count = fix_all_large_trees()
        
        # 创建测试树
        create_test_trees()
        
        print(f"\n" + "=" * 70)
        print("🎉 处理完成!")
        print(f"✅ 成功修复: {success_count} 个树文件")
        
        print(f"\n💡 使用建议:")
        print(f"1. 优先使用 *_simple_skeleton.tre 文件")
        print(f"2. 这些是最简单的星形树，完全兼容IQ-TREE")
        print(f"3. 如果还有问题，可以先用测试树验证")
        
        print(f"\n📝 IQ-TREE命令示例:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_simple_skeleton.tre -m MFP -bb 1000")
        print(f"iqtree2 -s concat.phy -p monocot_concat.txt -t monocot_simple_skeleton.tre -m MFP -bb 1000")
        
        print(f"\n🔍 如果仍有问题，尝试不使用约束树:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -m MFP -bb 1000")
        
        return True
        
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
