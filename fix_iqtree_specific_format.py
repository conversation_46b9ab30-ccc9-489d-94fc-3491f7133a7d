#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门为IQ-TREE修复species_tree.tre文件格式
解决连续括号问题，同时尽可能保持拓扑结构
"""

import os
import re

def fix_consecutive_brackets_for_iqtree(tree_string):
    """
    专门为IQ-TREE修复连续括号问题
    """
    original_tree = tree_string.strip()
    
    # 确保以分号结尾
    if not original_tree.endswith(';'):
        original_tree += ';'
    
    print(f"原始树长度: {len(original_tree)}")
    
    # 分析开头的连续括号
    opening_brackets = 0
    for char in original_tree:
        if char == '(':
            opening_brackets += 1
        else:
            break
    
    print(f"开头连续括号数: {opening_brackets}")
    
    if opening_brackets <= 3:
        print("括号数量可接受，无需修复")
        return original_tree
    
    # 方法1: 尝试减少连续括号的层数
    fixed_tree = original_tree
    
    # 如果开头有超过3个连续括号，尝试重新格式化
    if opening_brackets > 3:
        print(f"开头有{opening_brackets}个连续括号，尝试重新格式化...")
        
        # 找到第一个非括号字符的位置
        first_content_pos = 0
        for i, char in enumerate(original_tree):
            if char not in '()':
                first_content_pos = i
                break
        
        if first_content_pos > 0:
            # 分析树的结构，尝试重新组织
            # 这是一个复杂的操作，我们采用保守的方法
            
            # 方法: 尝试移除一些最外层的括号
            attempts = min(opening_brackets - 3, 5)  # 最多尝试移除5层
            
            for attempt in range(1, attempts + 1):
                test_tree = original_tree[attempt:-attempt] + ';'
                
                # 验证修改后的树是否仍然有效
                test_open = test_tree.count('(')
                test_close = test_tree.count(')')
                
                if test_open == test_close and test_open > 0:
                    # 检查物种数量是否保持
                    orig_species = set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', original_tree))
                    test_species = set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', test_tree))
                    
                    if len(orig_species) == len(test_species):
                        # 检查新的开头括号数
                        new_opening = 0
                        for char in test_tree:
                            if char == '(':
                                new_opening += 1
                            else:
                                break
                        
                        print(f"尝试移除{attempt}层: 新开头括号数 = {new_opening}")
                        
                        if new_opening <= 3:
                            fixed_tree = test_tree
                            print(f"✅ 成功移除{attempt}层括号")
                            break
                    else:
                        print(f"移除{attempt}层会丢失物种，跳过")
                else:
                    print(f"移除{attempt}层导致括号不平衡，跳过")
    
    # 最终检查
    final_opening = 0
    for char in fixed_tree:
        if char == '(':
            final_opening += 1
        else:
            break
    
    print(f"最终开头括号数: {final_opening}")
    print(f"修复后树长度: {len(fixed_tree)}")
    
    return fixed_tree

def create_alternative_format(tree_string):
    """
    如果无法修复原始格式，创建替代格式
    """
    print("创建替代格式...")
    
    # 提取所有物种
    species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
    species_list = list(set(re.findall(species_pattern, tree_string)))
    species_list.sort()
    
    print(f"提取到 {len(species_list)} 个物种")
    
    # 创建最简单的二分树格式
    if len(species_list) <= 2:
        return f"({','.join(species_list)});"
    
    # 将物种分成两组
    mid = len(species_list) // 2
    group1 = species_list[:mid]
    group2 = species_list[mid:]
    
    # 创建二分结构
    if len(group1) == 1:
        left_part = group1[0]
    else:
        left_part = f"({','.join(group1)})"
    
    if len(group2) == 1:
        right_part = group2[0]
    else:
        right_part = f"({','.join(group2)})"
    
    alternative_tree = f"({left_part},{right_part});"
    
    # 验证括号数
    alt_open = alternative_tree.count('(')
    alt_close = alternative_tree.count(')')
    
    print(f"替代树括号: {alt_open} 开, {alt_close} 关")
    print(f"替代树长度: {len(alternative_tree)}")
    
    return alternative_tree

def fix_species_tree_for_iqtree(input_file, output_file, alt_output_file):
    """
    为IQ-TREE修复species_tree文件
    """
    print(f"\n修复文件: {input_file}")
    print("-" * 60)
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 尝试修复原始格式
        fixed_tree = fix_consecutive_brackets_for_iqtree(original_tree)
        
        # 检查修复效果
        fixed_opening = 0
        for char in fixed_tree:
            if char == '(':
                fixed_opening += 1
            else:
                break
        
        # 保存修复后的树
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_tree)
        
        print(f"✅ 主要修复完成: {output_file}")
        
        # 如果仍然有问题，创建替代格式
        if fixed_opening > 3:
            print(f"⚠️ 仍有{fixed_opening}个连续括号，创建替代格式...")
            alt_tree = create_alternative_format(original_tree)
            
            with open(alt_output_file, 'w', encoding='utf-8') as f:
                f.write(alt_tree)
            
            print(f"✅ 替代格式完成: {alt_output_file}")
        
        # 验证结果
        orig_species = len(set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', original_tree)))
        fixed_species = len(set(re.findall(r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b', fixed_tree)))
        
        print(f"验证: 原始{orig_species}物种 -> 修复后{fixed_species}物种")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    tree_files = [
        "ANA_species_tree.tre",
        "dicots_species_tree.tre",
        "monocot_species_tree.tre",
        "superrosids_species_tree.tre",
        "superasterids_species_tree.tre"
    ]
    
    print("🔧 专门为IQ-TREE修复species_tree.tre文件")
    print("目标: 解决连续括号问题，保持拓扑结构")
    print("=" * 80)
    
    success_count = 0
    
    for tree_file in tree_files:
        if os.path.exists(tree_file):
            base_name = tree_file.replace('_species_tree.tre', '')
            output_file = f"{base_name}_iqtree_fixed.tre"
            alt_output_file = f"{base_name}_iqtree_alternative.tre"
            
            if fix_species_tree_for_iqtree(tree_file, output_file, alt_output_file):
                success_count += 1
        else:
            print(f"\n⚠️ 文件不存在: {tree_file}")
    
    print(f"\n" + "=" * 80)
    print(f"🎉 修复完成: {success_count}/{len(tree_files)} 个文件")
    
    if success_count > 0:
        print(f"\n💡 使用建议:")
        print(f"1. 优先使用 *_iqtree_fixed.tre 文件")
        print(f"2. 如果仍有问题，使用 *_iqtree_alternative.tre 文件")
        print(f"3. 最后选择：不使用约束树")
        
        print(f"\n📝 IQ-TREE命令示例:")
        print(f"# 优先尝试:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_iqtree_fixed.tre -m MFP -bb 1000")
        print(f"# 如果失败:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t superrosids_iqtree_alternative.tre -m MFP -bb 1000")
        print(f"# 最后选择:")
        print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -m MFP -bb 1000")
    
    return success_count > 0

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
