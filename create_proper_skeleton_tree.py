#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的骨架树：保持系统发育关系，但简化格式以兼容IQ-TREE
"""

import re
import os

def extract_species_and_structure(tree_string):
    """
    从原始骨架树中提取物种和基本结构信息
    """
    # 提取所有物种名称
    species_pattern = r'\b([A-Za-z][A-Za-z0-9_-]*_[A-Za-z0-9_-]*)\b'
    all_species = re.findall(species_pattern, tree_string)
    unique_species = sorted(list(set(all_species)))
    
    print(f"提取到 {len(unique_species)} 个物种")
    
    # 分析原始树的主要分组
    # 通过寻找大的分组模式来理解树的结构
    
    # 识别主要的科/属分组
    genus_groups = {}
    for species in unique_species:
        genus = species.split('_')[0]
        if genus not in genus_groups:
            genus_groups[genus] = []
        genus_groups[genus].append(species)
    
    print(f"识别到 {len(genus_groups)} 个属")
    
    # 显示主要属的物种数量
    large_genera = {genus: species_list for genus, species_list in genus_groups.items() if len(species_list) > 1}
    print(f"多物种属: {len(large_genera)} 个")
    
    return unique_species, genus_groups

def create_taxonomic_skeleton_tree(species_list, genus_groups):
    """
    基于分类学关系创建骨架树
    """
    print("创建基于分类学的骨架树...")
    
    # 根据ANA分类的已知系统发育关系创建结构
    # 主要分组：Amborellales, Nymphaeales, Austrobaileyales, Chloranthales, Canellales, Piperales, Magnoliales, Laurales
    
    # 定义主要分组（基于分类学知识）
    major_groups = {
        'Amborellales': ['Amborella'],
        'Nymphaeales': ['Nymphaea', 'Nuphar', 'Victoria', 'Euryale', 'Brasenia', 'Cabomba', 'Trithuria'],
        'Austrobaileyales': ['Austrobaileya', 'Trimenia', 'Schisandra', 'Kadsura', 'Illicium'],
        'Chloranthales': ['Chloranthus', 'Sarcandra'],
        'Canellales': ['Canella', 'Tasmannia'],
        'Piperales': ['Piper', 'Peperomia', 'Aristolochia', 'Asarum', 'Saruma', 'Anemopsis', 'Saururus', 'Houttuynia'],
        'Magnoliales': ['Magnolia', 'Liriodendron', 'Annona', 'Asimina', 'Guatteria', 'Cananga', 'Eupomatia', 'Polyalthia', 'Alphonsea', 'Monanthotaxis', 'Monoon', 'Cymbopetalum', 'Fissistigma', 'Cardiopetalum', 'Horsfieldia', 'Myristica'],
        'Laurales': ['Laurus', 'Persea', 'Cinnamomum', 'Lindera', 'Litsea', 'Ocotea', 'Calycanthus', 'Chimonanthus', 'Hernandia', 'Macrotorus']
    }
    
    # 将物种分配到主要分组
    grouped_species = {}
    unassigned_species = []
    
    for species in species_list:
        genus = species.split('_')[0]
        assigned = False
        
        for group_name, genera in major_groups.items():
            if genus in genera:
                if group_name not in grouped_species:
                    grouped_species[group_name] = []
                grouped_species[group_name].append(species)
                assigned = True
                break
        
        if not assigned:
            unassigned_species.append(species)
    
    print(f"分组结果:")
    for group_name, species_in_group in grouped_species.items():
        print(f"  {group_name}: {len(species_in_group)} 个物种")
    
    if unassigned_species:
        print(f"  未分组: {len(unassigned_species)} 个物种")
    
    # 创建分层的树结构
    group_parts = []
    
    for group_name in ['Amborellales', 'Nymphaeales', 'Austrobaileyales', 'Chloranthales', 'Canellales', 'Piperales', 'Magnoliales', 'Laurales']:
        if group_name in grouped_species:
            species_in_group = sorted(grouped_species[group_name])
            if len(species_in_group) == 1:
                group_parts.append(species_in_group[0])
            else:
                group_parts.append(f"({','.join(species_in_group)})")
    
    # 添加未分组的物种
    if unassigned_species:
        if len(unassigned_species) == 1:
            group_parts.append(unassigned_species[0])
        else:
            group_parts.append(f"({','.join(sorted(unassigned_species))})")
    
    # 创建最终的树结构
    if len(group_parts) <= 2:
        tree = f"({','.join(group_parts)});"
    else:
        # 创建更复杂的分层结构
        # 将ANA的主要分支分组
        basal_groups = []  # 基部类群
        core_groups = []   # 核心类群
        
        # 基部类群：Amborellales, Nymphaeales
        for group_name in ['Amborellales', 'Nymphaeales']:
            if group_name in grouped_species:
                species_in_group = sorted(grouped_species[group_name])
                if len(species_in_group) == 1:
                    basal_groups.append(species_in_group[0])
                else:
                    basal_groups.append(f"({','.join(species_in_group)})")
        
        # 核心类群：其他所有
        for group_name in ['Austrobaileyales', 'Chloranthales', 'Canellales', 'Piperales', 'Magnoliales', 'Laurales']:
            if group_name in grouped_species:
                species_in_group = sorted(grouped_species[group_name])
                if len(species_in_group) == 1:
                    core_groups.append(species_in_group[0])
                else:
                    core_groups.append(f"({','.join(species_in_group)})")
        
        # 添加未分组的物种到核心类群
        if unassigned_species:
            if len(unassigned_species) == 1:
                core_groups.append(unassigned_species[0])
            else:
                core_groups.append(f"({','.join(sorted(unassigned_species))})")
        
        # 构建最终树
        if basal_groups and core_groups:
            basal_part = ','.join(basal_groups) if len(basal_groups) > 1 else basal_groups[0]
            core_part = f"({','.join(core_groups)})" if len(core_groups) > 1 else core_groups[0]
            tree = f"({basal_part},{core_part});"
        else:
            # 如果只有一类，创建简单结构
            all_parts = basal_groups + core_groups
            tree = f"({','.join(all_parts)});"
    
    return tree

def main():
    """主函数"""
    input_file = "ANA_species_tree.tre"
    
    print("🌳 创建正确的ANA骨架树")
    print("目标: 保持分类学/系统发育关系，简化格式以兼容IQ-TREE")
    print("=" * 70)
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 提取物种和结构信息
        species_list, genus_groups = extract_species_and_structure(original_tree)
        
        # 创建分类学骨架树
        skeleton_tree = create_taxonomic_skeleton_tree(species_list, genus_groups)
        
        # 保存骨架树
        output_file = "ANA_taxonomic_skeleton_tree.tre"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(skeleton_tree)
        
        print(f"\n✅ 创建分类学骨架树: {output_file}")
        print(f"   长度: {len(skeleton_tree)} 字符")
        
        # 验证括号平衡
        open_count = skeleton_tree.count('(')
        close_count = skeleton_tree.count(')')
        print(f"   括号: {open_count} 开, {close_count} 关")
        print(f"   平衡: {'是' if open_count == close_count else '否'}")
        
        # 显示树的开头部分
        if len(skeleton_tree) > 200:
            print(f"   预览: {skeleton_tree[:200]}...")
        else:
            print(f"   完整树: {skeleton_tree}")
        
        print(f"\n💡 使用说明:")
        print(f"这个骨架树基于ANA分类的已知系统发育关系构建")
        print(f"主要分组: Amborellales, Nymphaeales, Austrobaileyales, Chloranthales, Canellales, Piperales, Magnoliales, Laurales")
        
        print(f"\n📝 IQ-TREE命令:")
        print(f"iqtree2 -s concat.phy -p ANA_concat.txt -t {output_file} -m MFP -bb 1000 -nt AUTO")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 分类学骨架树创建完成!")
            print(f"这个树保持了ANA分类的系统发育关系，同时兼容IQ-TREE")
        else:
            print(f"\n❌ 创建失败")
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
