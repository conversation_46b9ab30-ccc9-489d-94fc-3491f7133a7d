## ALIGNMENT FILE ##
alignment = concat.phy;

## BRANCHLENGTHS: linked | unlinked ##
branchlengths = linked;

## MODELS OF EVOLUTION: all | allx | mrbayes | beast | gamma | gammai | <list> ##
models = GTR, GTR+G, GTR+I+G;

# MODEL SELECTION: AIC | AICc | BIC #
model_selection = aicc;

## DATA BLOCKS: see manual for how to define ##
[data_blocks]
msa_18S_pos1 = 1-84\3;
msa_18S_pos2 = 2-84\3;
msa_18S_pos3 = 3-84\3;
msa_28S_pos1 = 85-2754\3;
msa_28S_pos2 = 86-2754\3;
msa_28S_pos3 = 87-2754\3;
msa_ITS_pos1 = 2755-2996\3;
msa_ITS_pos2 = 2756-2996\3;
msa_ITS_pos3 = 2757-2996\3;
msa_accD_pos1 = 2997-4469\3;
msa_accD_pos2 = 2998-4469\3;
msa_accD_pos3 = 2999-4469\3;
msa_atpA_pos1 = 4470-5978\3;
msa_atpA_pos2 = 4471-5978\3;
msa_atpA_pos3 = 4472-5978\3;
msa_atpB_pos1 = 5979-6686\3;
msa_atpB_pos2 = 5980-6686\3;
msa_atpB_pos3 = 5981-6686\3;
msa_atpE_pos1 = 6687-7085\3;
msa_atpE_pos2 = 6688-7085\3;
msa_atpE_pos3 = 6689-7085\3;
msa_atpF_pos1 = 7086-7628\3;
msa_atpF_pos2 = 7087-7628\3;
msa_atpF_pos3 = 7088-7628\3;
msa_atpH_pos1 = 7629-7872\3;
msa_atpH_pos2 = 7630-7872\3;
msa_atpH_pos3 = 7631-7872\3;
msa_atpI_pos1 = 7873-8616\3;
msa_atpI_pos2 = 7874-8616\3;
msa_atpI_pos3 = 7875-8616\3;
msa_ccsA_pos1 = 8617-9561\3;
msa_ccsA_pos2 = 8618-9561\3;
msa_ccsA_pos3 = 8619-9561\3;
msa_cemA_pos1 = 9562-10251\3;
msa_cemA_pos2 = 9563-10251\3;
msa_cemA_pos3 = 9564-10251\3;
msa_clpP_pos1 = 10252-10895\3;
msa_clpP_pos2 = 10253-10895\3;
msa_clpP_pos3 = 10254-10895\3;
msa_matK_pos1 = 10896-12428\3;
msa_matK_pos2 = 10897-12428\3;
msa_matK_pos3 = 10898-12428\3;
msa_ndhA_pos1 = 12429-13516\3;
msa_ndhA_pos2 = 12430-13516\3;
msa_ndhA_pos3 = 12431-13516\3;
msa_ndhB_pos1 = 13517-15049\3;
msa_ndhB_pos2 = 13518-15049\3;
msa_ndhB_pos3 = 13519-15049\3;
msa_ndhC_pos1 = 15050-15389\3;
msa_ndhC_pos2 = 15051-15389\3;
msa_ndhC_pos3 = 15052-15389\3;
msa_ndhD_pos1 = 15390-16892\3;
msa_ndhD_pos2 = 15391-16892\3;
msa_ndhD_pos3 = 15392-16892\3;
msa_ndhE_pos1 = 16893-17192\3;
msa_ndhE_pos2 = 16894-17192\3;
msa_ndhE_pos3 = 16895-17192\3;
msa_ndhF_pos1 = 17193-19382\3;
msa_ndhF_pos2 = 17194-19382\3;
msa_ndhF_pos3 = 17195-19382\3;
msa_ndhG_pos1 = 19383-19913\3;
msa_ndhG_pos2 = 19384-19913\3;
msa_ndhG_pos3 = 19385-19913\3;
msa_ndhH_pos1 = 19914-21089\3;
msa_ndhH_pos2 = 19915-21089\3;
msa_ndhH_pos3 = 19916-21089\3;
msa_ndhI_pos1 = 21090-21629\3;
msa_ndhI_pos2 = 21091-21629\3;
msa_ndhI_pos3 = 21092-21629\3;
msa_ndhJ_pos1 = 21630-22103\3;
msa_ndhJ_pos2 = 21631-22103\3;
msa_ndhJ_pos3 = 21632-22103\3;
msa_ndhK_pos1 = 22104-22983\3;
msa_ndhK_pos2 = 22105-22983\3;
msa_ndhK_pos3 = 22106-22983\3;

## SCHEMES, search: all | user | greedy | rcluster | rclusterf | kmeans ##
[schemes]
search = greedy;