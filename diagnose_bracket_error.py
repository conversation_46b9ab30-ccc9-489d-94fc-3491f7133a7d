#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确诊断IQ-TREE的"冗余双重括号"错误
找出第2083个字符位置的具体问题
"""

import re
import os

def analyze_character_position(tree_string, error_position):
    """
    分析错误位置周围的字符
    """
    print(f"分析第 {error_position} 个字符位置的错误...")
    
    # 获取错误位置周围的上下文
    start = max(0, error_position - 50)
    end = min(len(tree_string), error_position + 50)
    
    context = tree_string[start:end]
    relative_pos = error_position - start
    
    print(f"错误位置周围的上下文 (位置 {start}-{end}):")
    print(f"'{context}'")
    print(f"错误字符位置: {' ' * relative_pos}^")
    
    if error_position < len(tree_string):
        error_char = tree_string[error_position]
        print(f"错误字符: '{error_char}'")
    
    return context, relative_pos

def find_problematic_patterns(tree_string):
    """
    查找可能导致"冗余双重括号"错误的模式
    """
    print("查找问题模式...")
    
    patterns = [
        (r'\(\(', "连续开括号 (("),
        (r'\)\)', "连续关括号 ))"),
        (r'\(\(\(', "三重开括号 ((("),
        (r'\)\)\)', "三重关括号 )))"),
        (r'\(\(\(\(', "四重开括号 (((("),
        (r'\)\)\)\)', "四重关括号 ))))"),
        (r'\(\)', "空括号 ()"),
        (r'\(,', "开括号后直接逗号 (,"),
        (r',\)', "逗号后直接关括号 ,)"),
        (r',,', "连续逗号 ,,"),
    ]
    
    problems = []
    for pattern, description in patterns:
        matches = list(re.finditer(pattern, tree_string))
        if matches:
            print(f"  发现 {len(matches)} 处 {description}:")
            for i, match in enumerate(matches[:5]):  # 只显示前5个
                start, end = match.span()
                context_start = max(0, start - 20)
                context_end = min(len(tree_string), end + 20)
                context = tree_string[context_start:context_end]
                print(f"    位置 {start}-{end}: ...{context}...")
                problems.append((start, end, description, context))
            if len(matches) > 5:
                print(f"    ... 还有 {len(matches) - 5} 处")
    
    return problems

def fix_redundant_brackets_precisely(tree_string):
    """
    精确修复冗余括号，保持拓扑结构
    """
    print("开始精确修复...")
    
    fixed_tree = tree_string
    changes_made = []
    
    # 1. 修复空括号
    empty_brackets = re.findall(r'\(\)', fixed_tree)
    if empty_brackets:
        print(f"移除 {len(empty_brackets)} 个空括号")
        fixed_tree = re.sub(r'\(\)', '', fixed_tree)
        changes_made.append("移除空括号")
    
    # 2. 修复连续逗号
    double_commas = re.findall(r',,+', fixed_tree)
    if double_commas:
        print(f"修复 {len(double_commas)} 处连续逗号")
        fixed_tree = re.sub(r',,+', ',', fixed_tree)
        changes_made.append("修复连续逗号")
    
    # 3. 修复开括号后直接逗号
    bracket_comma = re.findall(r'\(,', fixed_tree)
    if bracket_comma:
        print(f"修复 {len(bracket_comma)} 处开括号后直接逗号")
        # 这种情况比较复杂，需要具体分析
        changes_made.append("发现开括号后直接逗号")
    
    # 4. 修复逗号后直接关括号
    comma_bracket = re.findall(r',\)', fixed_tree)
    if comma_bracket:
        print(f"修复 {len(comma_bracket)} 处逗号后直接关括号")
        # 这种情况也比较复杂，需要具体分析
        changes_made.append("发现逗号后直接关括号")
    
    # 5. 检查是否有过度嵌套的括号
    max_depth = 0
    current_depth = 0
    depth_positions = []
    
    for i, char in enumerate(fixed_tree):
        if char == '(':
            current_depth += 1
            max_depth = max(max_depth, current_depth)
            if current_depth > 10:  # 超过10层嵌套可能有问题
                depth_positions.append((i, current_depth))
        elif char == ')':
            current_depth -= 1
    
    print(f"最大括号嵌套深度: {max_depth}")
    if depth_positions:
        print(f"发现 {len(depth_positions)} 处深度嵌套 (>10层)")
    
    return fixed_tree, changes_made

def validate_tree_structure(tree_string):
    """
    验证树结构的有效性
    """
    print("验证树结构...")
    
    # 基本括号平衡
    open_count = tree_string.count('(')
    close_count = tree_string.count(')')
    print(f"括号平衡: {open_count} 开, {close_count} 关")
    
    if open_count != close_count:
        print("❌ 括号不平衡!")
        return False
    
    # 检查括号嵌套是否正确
    depth = 0
    for i, char in enumerate(tree_string):
        if char == '(':
            depth += 1
        elif char == ')':
            depth -= 1
            if depth < 0:
                print(f"❌ 位置 {i} 处关括号过多!")
                return False
    
    if depth != 0:
        print(f"❌ 最终括号深度不为0: {depth}")
        return False
    
    # 提取物种数量
    species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
    species = set(re.findall(species_pattern, tree_string))
    print(f"包含 {len(species)} 个物种")
    
    print("✅ 树结构基本有效")
    return True

def diagnose_and_fix_tree(input_file):
    """
    诊断并修复树文件
    """
    print(f"诊断文件: {input_file}")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    # 读取文件
    with open(input_file, 'r', encoding='utf-8') as f:
        tree_content = f.read().strip()
    
    print(f"文件大小: {len(tree_content)} 字符")
    
    # 分析第2083个字符位置（IQ-TREE报错位置）
    if len(tree_content) >= 2083:
        analyze_character_position(tree_content, 2083)
    
    # 查找问题模式
    problems = find_problematic_patterns(tree_content)
    
    # 验证原始结构
    print(f"\n原始树验证:")
    validate_tree_structure(tree_content)
    
    # 尝试修复
    print(f"\n开始修复...")
    fixed_tree, changes = fix_redundant_brackets_precisely(tree_content)
    
    if changes:
        print(f"进行的修改: {', '.join(changes)}")
        
        # 验证修复后的结构
        print(f"\n修复后验证:")
        if validate_tree_structure(fixed_tree):
            # 保存修复后的文件
            output_file = input_file.replace('.tre', '_FIXED.tre')
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(fixed_tree)
            print(f"✅ 修复完成: {output_file}")
            return output_file
        else:
            print("❌ 修复后结构仍有问题")
    else:
        print("未发现明显的格式问题")
    
    return None

def main():
    """主函数"""
    # 诊断报错的文件
    problem_file = "superrosids_iqtree_fixed.tre"
    
    if os.path.exists(problem_file):
        result = diagnose_and_fix_tree(problem_file)
        if result:
            print(f"\n🎉 请尝试使用修复后的文件:")
            print(f"iqtree2 -s concat.phy -p superrosids_concat.txt -t {result} -m MFP -bb 1000")
    else:
        print(f"文件不存在: {problem_file}")
        print("请先确认要诊断的文件名")

if __name__ == "__main__":
    main()
