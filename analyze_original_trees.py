#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析原始species_tree.tre文件的括号结构
找出IQ-TREE报错的具体原因
"""

import os
import re

def analyze_bracket_structure(tree_string, filename):
    """
    详细分析树的括号结构
    """
    print(f"\n分析文件: {filename}")
    print("=" * 60)
    
    # 基本统计
    open_brackets = tree_string.count('(')
    close_brackets = tree_string.count(')')
    commas = tree_string.count(',')
    
    print(f"总长度: {len(tree_string)} 字符")
    print(f"开括号: {open_brackets}")
    print(f"关括号: {close_brackets}")
    print(f"逗号数: {commas}")
    print(f"估计物种数: {commas + 1}")
    print(f"括号平衡: {'是' if open_brackets == close_brackets else '否'}")
    
    # 分析开头的连续括号
    opening_pattern = ""
    for i, char in enumerate(tree_string):
        if char == '(':
            opening_pattern += char
        else:
            break
    
    print(f"开头连续括号: '{opening_pattern}' (共{len(opening_pattern)}个)")
    
    # 分析结尾的连续括号
    ending_pattern = ""
    for char in reversed(tree_string.rstrip(';')):
        if char == ')':
            ending_pattern = char + ending_pattern
        else:
            break
    
    print(f"结尾连续括号: '{ending_pattern}' (共{len(ending_pattern)}个)")
    
    # 检查是否有问题的模式
    problematic_patterns = [
        (r'\(\(', "连续开括号"),
        (r'\)\)', "连续关括号"),
        (r'\(\)', "空括号"),
        (r'\(\(\(', "三重开括号"),
        (r'\)\)\)', "三重关括号")
    ]
    
    print(f"\n问题模式检查:")
    for pattern, description in problematic_patterns:
        matches = len(re.findall(pattern, tree_string))
        if matches > 0:
            print(f"  ⚠️ {description}: {matches} 处")
        else:
            print(f"  ✅ {description}: 无")
    
    # 检查最外层是否有冗余括号
    if tree_string.startswith('(') and tree_string.rstrip(';').endswith(')'):
        inner_content = tree_string[1:-2] if tree_string.endswith(';') else tree_string[1:-1]
        
        # 检查内容的括号平衡
        depth = 0
        min_depth = 0
        for char in inner_content:
            if char == '(':
                depth += 1
            elif char == ')':
                depth -= 1
            min_depth = min(min_depth, depth)
        
        if min_depth >= 0 and depth == 0:
            # 计算顶层的逗号数（不在括号内的逗号）
            top_level_commas = 0
            depth = 0
            for char in inner_content:
                if char == '(':
                    depth += 1
                elif char == ')':
                    depth -= 1
                elif char == ',' and depth == 0:
                    top_level_commas += 1
            
            print(f"\n最外层括号分析:")
            print(f"  顶层分支数: {top_level_commas + 1}")
            if top_level_commas == 0:
                print(f"  ⚠️ 最外层括号可能是冗余的（只有一个顶层分支）")
            else:
                print(f"  ✅ 最外层括号是必要的（多个顶层分支）")
    
    # 提取物种名称
    species_pattern = r'\b([A-Z][a-z]+_[a-z]+(?:_[a-z]+)*)\b'
    species_matches = re.findall(species_pattern, tree_string)
    unique_species = list(set(species_matches))
    
    print(f"\n物种信息:")
    print(f"  提取到的物种数: {len(unique_species)}")
    print(f"  前5个物种: {unique_species[:5]}")
    
    return {
        'open_brackets': open_brackets,
        'close_brackets': close_brackets,
        'balanced': open_brackets == close_brackets,
        'opening_pattern': opening_pattern,
        'ending_pattern': ending_pattern,
        'species_count': len(unique_species)
    }

def check_all_species_trees():
    """
    检查所有的species_tree.tre文件
    """
    tree_files = [
        "ANA_species_tree.tre",
        "dicots_species_tree.tre", 
        "monocot_species_tree.tre",
        "superrosids_species_tree.tre",
        "superasterids_species_tree.tre"
    ]
    
    print("🔍 分析所有原始species_tree.tre文件")
    print("目标: 找出IQ-TREE报错的具体原因")
    print("=" * 80)
    
    results = {}
    
    for tree_file in tree_files:
        if os.path.exists(tree_file):
            try:
                with open(tree_file, 'r', encoding='utf-8') as f:
                    tree_content = f.read().strip()
                
                results[tree_file] = analyze_bracket_structure(tree_content, tree_file)
                
            except Exception as e:
                print(f"\n❌ 读取 {tree_file} 时出错: {str(e)}")
        else:
            print(f"\n⚠️ 文件不存在: {tree_file}")
    
    # 总结分析
    print(f"\n" + "=" * 80)
    print("📊 总结分析")
    print("=" * 80)
    
    for tree_file, result in results.items():
        print(f"\n{tree_file}:")
        print(f"  开头括号: {result['opening_pattern']} ({len(result['opening_pattern'])}个)")
        print(f"  结尾括号: {result['ending_pattern']} ({len(result['ending_pattern'])}个)")
        print(f"  括号平衡: {'是' if result['balanced'] else '否'}")
        print(f"  物种数: {result['species_count']}")
        
        # 判断可能的问题
        if len(result['opening_pattern']) > 3:
            print(f"  ⚠️ 可能问题: 开头有{len(result['opening_pattern'])}个连续括号")
        if len(result['ending_pattern']) > 3:
            print(f"  ⚠️ 可能问题: 结尾有{len(result['ending_pattern'])}个连续括号")
    
    print(f"\n💡 建议:")
    print(f"1. 如果开头有多个连续括号，可能是冗余的最外层包装")
    print(f"2. IQ-TREE对连续括号很敏感，特别是超过2个的情况")
    print(f"3. 可以尝试移除最外层的冗余括号")
    print(f"4. 或者完全不使用约束树，让IQ-TREE自由构建")

def main():
    """主函数"""
    try:
        check_all_species_trees()
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
