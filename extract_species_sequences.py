import os 
from Bio import SeqIO

# 直接定义需要提取的物种名称
target_species = {
    "Equisetum_arvense",
    "Dryopteris_expansa", 
    "Lycopodium_obscurum",
    "Ginkgo_biloba",
    "<PERSON><PERSON>_kaempferi"
}

# 输入和输出目录路径
input_dir = "."  # 当前路径下的fasta文件
output_dir = "filtered_fasta250629"  # 存放提取出的序列

print(f"目标物种: {', '.join(sorted(target_species))}")
print(f"输入目录: {input_dir}")
print(f"输出目录: {output_dir}")

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

# 统计变量
total_files_processed = 0
total_sequences_found = 0
species_found_count = {species: 0 for species in target_species}

# 遍历当前路径下的所有 .fasta 文件
fasta_files = [f for f in os.listdir(input_dir) if f.endswith(".fasta")]
print(f"\n找到 {len(fasta_files)} 个FASTA文件")

for fasta_file in fasta_files:
    input_path = os.path.join(input_dir, fasta_file)
    output_path = os.path.join(output_dir, fasta_file)
    
    sequences_in_file = 0
    
    print(f"\n处理文件: {fasta_file}")
    
    # 筛选并提取匹配的序列
    with open(output_path, "w") as out_f:
        for record in SeqIO.parse(input_path, "fasta"):
            # 检查序列名称是否包含目标物种名称
            for species in target_species:
                if species in record.id:
                    SeqIO.write(record, out_f, "fasta")
                    sequences_in_file += 1
                    species_found_count[species] += 1
                    print(f"  找到序列: {record.id} (匹配物种: {species})")
                    break  # 找到匹配后跳出循环，避免重复计数
    
    if sequences_in_file > 0:
        print(f"  从 {fasta_file} 中提取了 {sequences_in_file} 条序列")
        total_sequences_found += sequences_in_file
    else:
        print(f"  {fasta_file} 中未找到目标物种的序列")
        # 如果没有找到序列，删除空的输出文件
        os.remove(output_path)
    
    total_files_processed += 1

# 输出统计结果
print(f"\n" + "="*60)
print("处理完成！统计结果:")
print(f"="*60)
print(f"处理的FASTA文件数: {total_files_processed}")
print(f"提取的序列总数: {total_sequences_found}")

print(f"\n各物种找到的序列数:")
for species in sorted(target_species):
    count = species_found_count[species]
    status = "✓" if count > 0 else "✗"
    print(f"  {status} {species}: {count} 条序列")

# 检查是否有物种未找到
missing_species = [species for species in target_species if species_found_count[species] == 0]
if missing_species:
    print(f"\n⚠ 未找到序列的物种:")
    for species in missing_species:
        print(f"  - {species}")
else:
    print(f"\n✓ 所有目标物种都找到了序列！")

print(f"\n输出文件保存在: {output_dir}/")
