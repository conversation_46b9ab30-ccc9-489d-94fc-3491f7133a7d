#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按照ANA骨架树的同样处理方式，为monocot_species_tree.tre创建正确的骨架树
"""

import re
import os

def extract_species_from_tree(tree_string):
    """从树字符串中提取所有物种名称"""
    species_pattern = r'\b([A-Za-z][A-Za-z0-9_-]*_[A-Za-z0-9_-]*)\b'
    all_species = re.findall(species_pattern, tree_string)
    unique_species = sorted(list(set(all_species)))
    return unique_species

def create_monocot_taxonomic_skeleton_tree(species_list):
    """
    基于单子叶植物分类学关系创建骨架树
    按照ANA骨架树的同样方法
    """
    print("创建单子叶植物分类学骨架树...")
    
    # 单子叶植物主要目的分类（基于APG IV系统）
    monocot_orders = {
        'Acorales': ['Acorus'],
        'Alismatales': [
            # 泽泻科
            'Alisma', 'Aponogeton', 'Butomus', 'Hydrocharis', 'Potamogeton', 'Sagittaria', 
            'Vallisneria', 'Zannichellia', 'Zostera', 'Cymodocea', 'Halophila', 'Najas', 
            'Ruppia', 'Scheuchzeria', 'Triglochin',
            # 天南星科
            'Alocasia', 'Amorphophallus', 'Anthurium', 'Arisaema', 'Arisarum', 'Arum', 
            'Caladium', 'Calla', 'Colocasia', 'Cryptocoryne', 'Dieffenbachia', 'Dracunculus', 
            'Epipremnum', 'Homalomena', 'Monstera', 'Philodendron', 'Pistia', 'Spathiphyllum', 
            'Symplocarpus', 'Xanthosoma', 'Zantedeschia'
        ],
        'Petrosaviales': ['Japonolirion', 'Petrosavia'],
        'Dioscoreales': ['Dioscorea', 'Tamus', 'Tacca'],
        'Pandanales': ['Cyclanthus', 'Carludovica', 'Freycinetia', 'Pandanus'],
        'Liliales': [
            'Alstroemeria', 'Bomarea', 'Calochortus', 'Cardiocrinum', 'Chamaelirium', 
            'Clintonia', 'Disporum', 'Erythronium', 'Fritillaria', 'Gagea', 'Lilium', 
            'Lloydia', 'Medeola', 'Nomocharis', 'Notholirion', 'Paris', 'Prosartes', 
            'Streptopus', 'Tricyrtis', 'Tulipa', 'Uvularia'
        ],
        'Asparagales': [
            # 传统天门冬目属
            'Agapanthus', 'Agave', 'Albuca', 'Allium', 'Aloe', 'Amaryllis', 'Anthericum', 
            'Asparagus', 'Asphodeline', 'Asphodelus', 'Aspidistra', 'Bellevalia', 'Bowiea', 
            'Brodiaea', 'Bulbine', 'Camassia', 'Chlorophytum', 'Colchicum', 'Convallaria', 
            'Cordyline', 'Crinum', 'Crocus', 'Curculigo', 'Cyrtanthus', 'Dianella', 'Dipcadi', 
            'Dracaena', 'Drimia', 'Drimiopsis', 'Erythronium', 'Eucomis', 'Fritillaria', 
            'Gagea', 'Galanthus', 'Gasteria', 'Gethyllis', 'Gladiolus', 'Gloriosa', 'Haemanthus', 
            'Haworthia', 'Hemerocallis', 'Herreria', 'Hesperaloe', 'Hippeastrum', 'Hosta', 
            'Hyacinthella', 'Hyacinthoides', 'Hyacinthus', 'Hypoxis', 'Ipheion', 'Iris', 
            'Ixia', 'Kniphofia', 'Lachenalia', 'Leucojum', 'Lilium', 'Maianthemum', 'Muscari', 
            'Narcissus', 'Nothoscordum', 'Ornithogalum', 'Paradisea', 'Paris', 'Polygonatum', 
            'Puschkinia', 'Ruscus', 'Sansevieria', 'Scilla', 'Smilax', 'Trillium', 'Tulipa', 
            'Urginea', 'Veratrum', 'Yucca', 'Zigadenus', 'Sisyrinchium', 'Ophiopogon',
            # 兰科属（重要！）
            'Phalaenopsis', 'Dendrobium', 'Paphiopedilum', 'Dracula', 'Pleurothallis', 
            'Stelis', 'Oncidium', 'Cymbidium', 'Cattleya', 'Orchis', 'Ophrys', 
            'Dactylorhiza', 'Platanthera', 'Gymnadenia', 'Anacamptis', 'Serapias',
            'Himantoglossum', 'Aceras', 'Neotinea', 'Bulbophyllum', 'Epidendrum',
            'Masdevallia', 'Maxillaria', 'Miltonia', 'Odontoglossum', 'Vanda',
            'Vanilla', 'Zygopetalum', 'Aerides', 'Angraecum', 'Brassavola',
            'Brassia', 'Calanthe', 'Catasetum', 'Coelogyne', 'Cycnoches',
            'Cypripedium', 'Disa', 'Gongora', 'Laelia', 'Lycaste', 'Phaius',
            'Phragmipedium', 'Renanthera', 'Rhynchostylis', 'Sobralia', 'Stanhopea',
            'Tolumnia', 'Trichocentrum', 'Aerangis', 'Ansellia', 'Ascocentrum', 
            'Bletilla', 'Camaridium', 'Chysis', 'Cochleanthes', 'Comparettia', 
            'Coryanthes', 'Dimerandra', 'Encyclia', 'Eria', 'Galeandra', 'Gomesa', 
            'Habenaria', 'Huntleya', 'Ionopsis', 'Isochilus', 'Jumellea', 'Kefersteinia', 
            'Lepanthes', 'Liparis', 'Ludisia', 'Macodes', 'Malaxis', 'Mexipedium', 
            'Mormodes', 'Neofinetia', 'Neostylis', 'Notylia', 'Oeceoclades', 
            'Ornithocephalus', 'Pabstia', 'Pescatorea', 'Polystachya', 'Promenaea', 
            'Psychopsis', 'Restrepia', 'Rodriguezia', 'Rossioglossum', 'Scaphosepalum', 
            'Scuticaria', 'Sigmatostalix', 'Sophronitis', 'Spathoglottis', 'Spiranthes', 
            'Stenorrhynchos', 'Trichopilia', 'Trigonidium', 'Warczewiczella', 'Warrea', 
            'Xylobium'
        ],
        'Arecales': [
            # 棕榈科
            'Acanthophoenix', 'Archontophoenix', 'Areca', 'Arenga', 'Astrocaryum', 'Attalea', 
            'Bactris', 'Balaka', 'Basselinia', 'Beccariophoenix', 'Bentinckia', 'Bismarckia', 
            'Borassodendron', 'Borassus', 'Brahea', 'Brassiophoenix', 'Burretiokentia', 'Butia', 
            'Calamus', 'Calyptrocalyx', 'Calyptronoma', 'Carpentaria', 'Carpoxylon', 'Caryota', 
            'Ceroxylon', 'Chamaedorea', 'Chamaerops', 'Chambeyronia', 'Chelyocarpus', 'Chuniophoenix', 
            'Clinosperma', 'Clinostigma', 'Coccothrinax', 'Cocos', 'Colpothrinax', 'Copernicia', 
            'Corypha', 'Cryosophila', 'Cyphophoenix', 'Cyphosperma', 'Cyrtostachys', 'Deckenia', 
            'Desmoncus', 'Dictyocaryum', 'Dictyosperma', 'Dransfieldia', 'Drymophloeus', 'Dypsis', 
            'Elaeis', 'Eleiodoxa', 'Eugeissona', 'Euterpe', 'Gaussia', 'Geonoma', 'Guihaia', 
            'Hedyscepe', 'Heterospathe', 'Howea', 'Hydriastele', 'Hyophorbe', 'Hyospathe', 
            'Hyphaene', 'Iriartea', 'Johannesteijsmannia', 'Jubaea', 'Jubaeopsis', 'Kentiopsis', 
            'Kerriodoxa', 'Latania', 'Lepidorrhachis', 'Licuala', 'Livistona', 'Lodoicea', 
            'Loxococcus', 'Manicaria', 'Mauritia', 'Mauritiella', 'Maximiliana', 'Medemia', 
            'Metroxylon', 'Nannorrhops', 'Neodypsis', 'Neoveitchia', 'Nephrosperma', 'Normanbya', 
            'Nypa', 'Oenocarpus', 'Oncosperma', 'Orania', 'Parajubaea', 'Phoenix', 'Physokentia', 
            'Pinanga', 'Plectocomia', 'Plectocomiopsis', 'Podococcus', 'Prestoea', 'Pritchardia', 
            'Pseudophoenix', 'Ptychococcus', 'Ptychosperma', 'Raphia', 'Ravenea', 'Reinhardtia', 
            'Rhapis', 'Roystonea', 'Sabal', 'Salacca', 'Satakentia', 'Serenoa', 'Socratea', 
            'Syagrus', 'Synechanthus', 'Thrinax', 'Trachycarpus', 'Trithrinax', 'Veitchia', 
            'Verschaffeltia', 'Wallichia', 'Washingtonia', 'Welfia', 'Wodyetia', 'Zombia'
        ],
        'Poales': [
            # 禾本科
            'Acidosasa', 'Bambusa', 'Bashania', 'Chimonobambusa', 'Chusquea', 'Dendrocalamus', 
            'Fargesia', 'Gigantochloa', 'Guadua', 'Indocalamus', 'Indosasa', 'Phyllostachys', 
            'Pseudosasa', 'Sasa', 'Semiarundinaria', 'Shibataea', 'Thamnocalamus', 'Yushania',
            'Achnatherum', 'Aegilops', 'Aeluropus', 'Agropyron', 'Agrostis', 'Aira', 'Alopecurus', 
            'Ammophila', 'Andropogon', 'Anemanthele', 'Anthoxanthum', 'Apera', 'Aristida', 
            'Arrhenatherum', 'Arundo', 'Austroderia', 'Austrostipa', 'Avena', 'Avenella', 'Avenula',
            'Beckmannia', 'Brachyelytrum', 'Brachypodium', 'Briza', 'Bromus', 'Calamagrostis',
            'Catabrosa', 'Catapodium', 'Cenchrus', 'Centotheca', 'Chasmanthium', 'Chionochloa',
            'Chloris', 'Chrysopogon', 'Cinna', 'Cleistogenes', 'Coleanthus', 'Colpodium',
            'Corynephorus', 'Cynodon', 'Cynosurus', 'Dactylis', 'Dactyloctenium', 'Danthonia',
            'Dasypyrum', 'Deschampsia', 'Desmazeria', 'Dichanthelium', 'Dichanthium', 'Digitaria',
            'Echinochloa', 'Echinopogon', 'Ehrharta', 'Eleusine', 'Elymus', 'Enteropogon',
            'Eragrostis', 'Eremochloa', 'Eremopyrum', 'Eriochloa', 'Festuca', 'Gaudinia',
            'Glyceria', 'Harpachne', 'Helictochloa', 'Helictotrichon', 'Hesperostipa', 'Heteranthelium',
            'Holcus', 'Hordelymus', 'Hordeum', 'Hygroryza', 'Hyparrhenia', 'Imperata',
            'Lagurus', 'Lamarckia', 'Leersia', 'Lepturus', 'Leymus', 'Lolium', 'Melica',
            'Milium', 'Miscanthus', 'Molinia', 'Muhlenbergia', 'Nardus', 'Nassella',
            'Oryzopsis', 'Panicum', 'Parapholis', 'Paspalum', 'Pennisetum', 'Phalaris',
            'Phleum', 'Phragmites', 'Piptatherum', 'Poa', 'Polypogon', 'Pseudoroegneria',
            'Puccinellia', 'Saccharum', 'Schismus', 'Secale', 'Setaria', 'Sorghum',
            'Spartina', 'Sphenopholis', 'Sporobolus', 'Stipa', 'Themeda', 'Trisetum',
            'Triticum', 'Urochloa', 'Vulpia', 'Zea', 'Zizania',
            # 莎草科
            'Carex', 'Cyperus', 'Eleocharis', 'Eriophorum', 'Fimbristylis', 'Fuirena', 
            'Rhynchospora', 'Schoenoplectus', 'Scirpus', 'Scleria', 'Ficinia', 'Schoenus',
            # 灯心草科
            'Juncus', 'Luzula',
            # 凤梨科
            'Aechmea', 'Ananas', 'Billbergia', 'Bromelia', 'Cryptanthus', 'Dyckia', 'Guzmania', 
            'Hechtia', 'Neoregelia', 'Pitcairnia', 'Puya', 'Tillandsia', 'Vriesea',
            # 灯心草科 (Restionaceae)
            'Restio', 'Elegia', 'Thamnochortus',
            # 香蒲科
            'Typha', 'Sparganium',
            # 谷精草科
            'Eriocaulon',
            # 黄眼草科
            'Xyris',
            # 花蔺科
            'Mayaca'
        ],
        'Commelinales': [
            'Commelina', 'Callisia', 'Gibasis', 'Tradescantia', 'Haemodorum', 'Anigozanthos', 
            'Conostylis', 'Hanguana', 'Philydrum', 'Pontederia', 'Eichhornia', 'Heteranthera', 
            'Monochoria'
        ],
        'Zingiberales': [
            # 姜科
            'Alpinia', 'Amomum', 'Boesenbergia', 'Costus', 'Curcuma', 'Elettaria', 'Etlingera', 
            'Globba', 'Hedychium', 'Kaempferia', 'Renealmia', 'Zingiber',
            # 美人蕉科
            'Canna',
            # 竹芋科
            'Calathea', 'Ctenanthe', 'Goeppertia', 'Ischnosiphon', 'Maranta', 'Monotagma', 
            'Myrosma', 'Sarcophrynium', 'Stromanthe', 'Thalia', 'Thaumatococcus',
            # 旅人蕉科
            'Heliconia', 'Strelitzia', 'Ravenala', 'Phenakospermum',
            # 芭蕉科
            'Ensete', 'Musa', 'Musella',
            # 兰花蕉科
            'Orchidantha', 'Lowia'
        ]
    }
    
    # 将物种分配到主要目
    grouped_species = {}
    unassigned_species = []
    
    for species in species_list:
        genus = species.split('_')[0]
        assigned = False
        
        for order_name, genera in monocot_orders.items():
            if genus in genera:
                if order_name not in grouped_species:
                    grouped_species[order_name] = []
                grouped_species[order_name].append(species)
                assigned = True
                break
        
        if not assigned:
            unassigned_species.append(species)
    
    print(f"分组结果:")
    for order_name, species_in_order in grouped_species.items():
        print(f"  {order_name}: {len(species_in_order)} 个物种")
    
    if unassigned_species:
        print(f"  未分组: {len(unassigned_species)} 个物种")
    
    # 创建分层的树结构（按照单子叶植物的系统发育关系）
    order_parts = []
    
    # 按照单子叶植物的系统发育关系排序
    order_sequence = ['Acorales', 'Alismatales', 'Petrosaviales', 'Dioscoreales', 'Pandanales', 
                     'Liliales', 'Asparagales', 'Arecales', 'Poales', 'Commelinales', 'Zingiberales']
    
    for order_name in order_sequence:
        if order_name in grouped_species:
            species_in_order = sorted(grouped_species[order_name])
            if len(species_in_order) == 1:
                order_parts.append(species_in_order[0])
            elif len(species_in_order) <= 10:
                order_parts.append(f"({','.join(species_in_order)})")
            else:
                # 对于大的目，进一步分组
                mid = len(species_in_order) // 2
                left_group = species_in_order[:mid]
                right_group = species_in_order[mid:]
                
                left_part = f"({','.join(left_group)})" if len(left_group) > 1 else left_group[0]
                right_part = f"({','.join(right_group)})" if len(right_group) > 1 else right_group[0]
                
                order_parts.append(f"({left_part},{right_part})")
    
    # 添加未分组的物种
    if unassigned_species:
        if len(unassigned_species) <= 10:
            if len(unassigned_species) == 1:
                order_parts.append(unassigned_species[0])
            else:
                order_parts.append(f"({','.join(sorted(unassigned_species))})")
        else:
            # 对未分组的物种也进行分组
            mid = len(unassigned_species) // 2
            left_group = sorted(unassigned_species)[:mid]
            right_group = sorted(unassigned_species)[mid:]
            
            left_part = f"({','.join(left_group)})"
            right_part = f"({','.join(right_group)})"
            
            order_parts.append(f"({left_part},{right_part})")
    
    # 创建最终的树结构（按照单子叶植物系统发育关系）
    if len(order_parts) <= 2:
        tree = f"({','.join(order_parts)});"
    elif len(order_parts) <= 4:
        tree = f"({','.join(order_parts)});"
    else:
        # 创建基于系统发育关系的分层结构
        # 基部类群：Acorales, Alismatales, Petrosaviales
        basal_orders = []
        core_orders = []
        
        for i, order_part in enumerate(order_parts):
            if i < 3:  # 前3个是基部类群
                basal_orders.append(order_part)
            else:
                core_orders.append(order_part)
        
        if len(basal_orders) == 1:
            basal_part = basal_orders[0]
        else:
            basal_part = f"({','.join(basal_orders)})"
        
        if len(core_orders) == 1:
            core_part = core_orders[0]
        else:
            core_part = f"({','.join(core_orders)})"
        
        tree = f"({basal_part},{core_part});"
    
    return tree

def main():
    """主函数"""
    input_file = "monocot_species_tree.tre"
    output_file = "monocot_taxonomic_skeleton_tree.tre"
    
    print("🌳 创建monocot分类学骨架树")
    print("目标: 基于单子叶植物分类学关系，按照ANA骨架树的同样方法")
    print("=" * 70)
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    try:
        # 读取原始树
        with open(input_file, 'r', encoding='utf-8') as f:
            original_tree = f.read().strip()
        
        # 提取物种
        species_list = extract_species_from_tree(original_tree)
        print(f"提取到 {len(species_list)} 个物种")
        
        # 创建分类学骨架树
        skeleton_tree = create_monocot_taxonomic_skeleton_tree(species_list)
        
        # 保存骨架树
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(skeleton_tree)
        
        # 验证新树
        open_count = skeleton_tree.count('(')
        close_count = skeleton_tree.count(')')
        
        print(f"\n✅ 创建成功!")
        print(f"📊 统计信息:")
        print(f"   长度: {len(skeleton_tree)} 字符")
        print(f"   括号: {open_count} 开, {close_count} 关")
        print(f"   平衡: {'是' if open_count == close_count else '否'}")
        print(f"   物种数: {len(species_list)}")
        
        print(f"\n📁 输出文件: {output_file}")
        print(f"💡 使用命令:")
        print(f"iqtree2 -s concat.phy -p monocot_concat.txt -t {output_file} -m MFP -bb 1000")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 monocot分类学骨架树创建完成!")
            print(f"这个树基于单子叶植物的系统发育关系，兼容IQ-TREE")
        else:
            print(f"\n❌ 创建失败")
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
