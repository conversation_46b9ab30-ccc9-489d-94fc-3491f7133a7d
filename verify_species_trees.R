# 验证生成的物种树文件

library(ape)

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

cat("验证生成的物种树文件\n")
cat(paste(rep("=", 60), collapse = ""), "\n\n")

# 分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  species_tree_file <- paste0(class_name, "_species_tree.tre")
  
  cat("验证", class_name, "物种树:\n")
  
  if (!file.exists(species_tree_file)) {
    cat("  ✗ 物种树文件不存在\n\n")
    next
  }
  
  # 读取原始分类数据
  class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
  expected_species <- class_data$wcvp_searchedName
  expected_species_count <- length(expected_species)
  
  # 读取物种树文件内容
  tree_content <- readLines(species_tree_file)
  tree_string <- tree_content[1]
  
  cat("  - 预期物种数:", expected_species_count, "\n")
  cat("  - 树文件大小:", nchar(tree_string), "个字符\n")
  
  # 检查树字符串中的物种数量
  # 计算逗号数量（物种之间的分隔符）
  comma_count <- length(gregexpr(",", tree_string)[[1]])
  if (comma_count == -1) comma_count <- 0
  
  # 计算括号对数量（科的数量）
  open_paren_count <- length(gregexpr("\\(", tree_string)[[1]])
  if (open_paren_count == -1) open_paren_count <- 0
  
  cat("  - 树中逗号数量:", comma_count, "\n")
  cat("  - 树中括号对数量:", open_paren_count, "\n")
  
  # 提取树中的所有物种名称
  # 使用正则表达式提取所有物种名称
  species_pattern <- "([A-Za-z_]+)"
  species_matches <- gregexpr(species_pattern, tree_string)
  species_in_tree <- regmatches(tree_string, species_matches)[[1]]
  
  # 过滤掉可能的非物种名称（如单个字母等）
  species_in_tree <- species_in_tree[nchar(species_in_tree) > 3]
  
  cat("  - 树中提取的物种数:", length(species_in_tree), "\n")
  
  # 检查是否所有预期物种都在树中
  missing_species <- setdiff(expected_species, species_in_tree)
  extra_species <- setdiff(species_in_tree, expected_species)
  
  if (length(missing_species) == 0 && length(extra_species) == 0) {
    cat("  ✓ 所有物种都正确包含在树中\n")
  } else {
    if (length(missing_species) > 0) {
      cat("  ⚠ 缺失物种数:", length(missing_species), "\n")
      if (length(missing_species) <= 5) {
        cat("    缺失物种:", paste(missing_species, collapse = ", "), "\n")
      }
    }
    if (length(extra_species) > 0) {
      cat("  ⚠ 多余物种数:", length(extra_species), "\n")
      if (length(extra_species) <= 5) {
        cat("    多余物种:", paste(extra_species, collapse = ", "), "\n")
      }
    }
  }
  
  # 显示树的一小部分作为示例
  if (nchar(tree_string) > 200) {
    cat("  - 树的开头部分:", substr(tree_string, 1, 200), "...\n")
  } else {
    cat("  - 完整树:", tree_string, "\n")
  }
  
  cat("\n")
}

cat("验证完成！\n")
cat("\n使用说明:\n")
cat("- 生成的物种树保持了原始骨架树的系统发育结构\n")
cat("- 每个科的位置被替换为该科下的所有物种\n")
cat("- 物种名称用逗号分隔，整个科用括号包围\n")
cat("- 这些树可以用于物种级别的系统发育分析\n")
