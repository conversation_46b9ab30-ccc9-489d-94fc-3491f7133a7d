# 最终结果总结 - 使用R语言生成的系统发育树小树

# 加载必要的包
if (!require("ape", quietly = TRUE)) {
  install.packages("ape", repos = "https://cran.r-project.org/")
  library(ape)
}

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

cat(paste(rep("=", 80), collapse = ""), "\n")
cat("系统发育树小树提取结果总结 (使用R语言ape包)\n")
cat(paste(rep("=", 80), collapse = ""), "\n\n")

# 分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

cat("1. 原始数据统计:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")

total_species <- 0
total_families <- c()

for (class_file in class_files) {
  if (file.exists(class_file)) {
    class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
    families <- unique(class_data$wcvp_family)
    total_families <- c(total_families, families)
    
    class_name <- gsub(".txt", "", class_file)
    cat(sprintf("%-15s: %5d 个物种, %3d 个科\n", class_name, nrow(class_data), length(families)))
    total_species <- total_species + nrow(class_data)
  }
}

unique_families <- unique(total_families)
cat(sprintf("%-15s: %5d 个物种, %3d 个不同的科\n", "总计", total_species, length(unique_families)))

cat("\n2. 生成的系统发育树文件:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")

for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  cat(sprintf("\n%s 类别:\n", class_name))
  
  # 系统发育子树
  phylo_file <- paste0(class_name, "_phylo_subtree.tre")
  if (file.exists(phylo_file)) {
    phylo_tree <- read.tree(phylo_file)
    cat(sprintf("  ✓ %s\n", phylo_file))
    cat(sprintf("    - 保持原始系统发育关系的子树\n"))
    cat(sprintf("    - 包含 %d 个科\n", length(phylo_tree$tip.label)))
  }
  
  # 简单星形树
  simple_file <- paste0(class_name, "_simple_subtree.tre")
  if (file.exists(simple_file)) {
    simple_tree <- read.tree(simple_file)
    cat(sprintf("  ✓ %s\n", simple_file))
    cat(sprintf("    - 简单星形树结构\n"))
    cat(sprintf("    - 包含 %d 个科\n", length(simple_tree$tip.label)))
  }
}

cat("\n3. 树文件验证:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")

for (class_file in class_files) {
  class_name <- gsub(".txt", "", class_file)
  
  # 读取原始分类数据
  if (file.exists(class_file)) {
    class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
    expected_families <- sort(unique(class_data$wcvp_family))
    
    # 验证系统发育子树
    phylo_file <- paste0(class_name, "_phylo_subtree.tre")
    if (file.exists(phylo_file)) {
      phylo_tree <- read.tree(phylo_file)
      phylo_families <- sort(phylo_tree$tip.label)
      
      if (identical(expected_families, phylo_families)) {
        cat(sprintf("✓ %s: 系统发育子树包含所有预期的科\n", class_name))
      } else {
        cat(sprintf("✗ %s: 系统发育子树科名不匹配\n", class_name))
      }
    }
    
    # 验证简单星形树
    simple_file <- paste0(class_name, "_simple_subtree.tre")
    if (file.exists(simple_file)) {
      simple_tree <- read.tree(simple_file)
      simple_families <- sort(simple_tree$tip.label)
      
      if (identical(expected_families, simple_families)) {
        cat(sprintf("✓ %s: 简单星形树包含所有预期的科\n", class_name))
      } else {
        cat(sprintf("✗ %s: 简单星形树科名不匹配\n", class_name))
      }
    }
  }
}

cat("\n4. 使用建议:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("• *_phylo_subtree.tre: 适用于需要保持系统发育关系的分析\n")
cat("  - 保留了原始树中各科之间的进化关系\n")
cat("  - 适合用于比较系统发育分析\n")
cat("  - 可用于计算系统发育多样性指数\n\n")

cat("• *_simple_subtree.tre: 适用于只关注科名列表的分析\n")
cat("  - 星形树结构，所有科等距离\n")
cat("  - 适合用于群落生态学分析\n")
cat("  - 文件更小，处理更快\n\n")

cat("5. 技术信息:\n")
cat(paste(rep("-", 50), collapse = ""), "\n")
cat("• 使用R语言 ape 包进行系统发育树处理\n")
cat("• keep.tip() 函数用于提取系统发育子树\n")
cat("• stree() 函数用于生成简单星形树\n")
cat("• 所有树文件均为标准Newick格式\n")

cat("\n", paste(rep("=", 80), collapse = ""), "\n")
cat("处理完成！所有系统发育树小树已成功生成。\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
