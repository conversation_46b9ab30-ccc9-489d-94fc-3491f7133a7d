#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将concat.cfg文件格式转换为类似Fabales.part的格式
输出文件为ANA_concat.txt
"""

import re
import os

def parse_concat_cfg(filename):
    """
    解析concat.cfg文件，提取基因信息
    """
    gene_ranges = {}
    
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找[data_blocks]部分
    in_data_blocks = False
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行和注释
        if not line or line.startswith('#') or line.startswith('##'):
            continue
            
        # 检查是否进入data_blocks部分
        if line == '[data_blocks]':
            in_data_blocks = True
            continue
        
        # 检查是否离开data_blocks部分
        if line.startswith('[') and line != '[data_blocks]':
            in_data_blocks = False
            continue
        
        # 解析data_blocks中的行
        if in_data_blocks and '=' in line:
            # 解析格式: msa_18S_pos1 = 1-1809\3;
            parts = line.split('=')
            if len(parts) == 2:
                gene_info = parts[0].strip()
                range_info = parts[1].strip().rstrip(';')
                
                # 提取基因名称（移除msa_前缀和_pos后缀）
                if gene_info.startswith('msa_') and '_pos' in gene_info:
                    gene_name = gene_info.replace('msa_', '').split('_pos')[0]
                    
                    # 解析范围信息
                    if '\\3' in range_info:
                        # 格式: 1-1809\3 表示从1到1809，步长为3
                        range_part = range_info.split('\\')[0]
                        if '-' in range_part:
                            start, end = range_part.split('-')
                            start = int(start.strip())
                            end = int(end.strip())
                            
                            # 存储基因的完整范围（不考虑密码子位置）
                            if gene_name not in gene_ranges:
                                gene_ranges[gene_name] = {'start': start, 'end': end}
                            else:
                                # 更新范围以包含所有位置
                                gene_ranges[gene_name]['start'] = min(gene_ranges[gene_name]['start'], start)
                                gene_ranges[gene_name]['end'] = max(gene_ranges[gene_name]['end'], end)
    
    return gene_ranges

def write_part_format(gene_ranges, output_filename):
    """
    将基因范围信息写入part格式文件
    """
    # 按起始位置排序
    sorted_genes = sorted(gene_ranges.items(), key=lambda x: x[1]['start'])
    
    with open(output_filename, 'w', encoding='utf-8') as f:
        for gene_name, range_info in sorted_genes:
            start = range_info['start']
            end = range_info['end']
            f.write(f"DNA, {gene_name} = {start}-{end}\n")
    
    return len(sorted_genes)

def main():
    """主函数"""
    input_file = "concat.cfg"
    output_file = "ANA_concat.txt"
    
    print("🔄 concat.cfg格式转换工具")
    print("=" * 60)
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件 {input_file} 不存在！")
        return False
    
    try:
        # 解析concat.cfg文件
        print(f"\n🔍 正在解析 {input_file}...")
        gene_ranges = parse_concat_cfg(input_file)
        
        if not gene_ranges:
            print("❌ 错误: 未找到有效的基因信息！")
            return False
        
        print(f"✅ 成功解析 {len(gene_ranges)} 个基因")
        
        # 显示前几个基因作为预览
        print(f"\n📋 基因信息预览（前10个）:")
        sorted_genes = sorted(gene_ranges.items(), key=lambda x: x[1]['start'])
        for i, (gene_name, range_info) in enumerate(sorted_genes[:10], 1):
            start = range_info['start']
            end = range_info['end']
            length = end - start + 1
            print(f"   {i:2d}. {gene_name:15} = {start:5d}-{end:5d} (长度: {length:4d})")
        
        if len(gene_ranges) > 10:
            print(f"   ... 还有 {len(gene_ranges) - 10} 个基因")
        
        # 写入part格式文件
        print(f"\n💾 正在写入 {output_file}...")
        gene_count = write_part_format(gene_ranges, output_file)
        
        print(f"✅ 转换完成!")
        print(f"📊 统计信息:")
        print(f"   • 处理的基因数: {gene_count}")
        print(f"   • 输出文件: {os.path.abspath(output_file)}")
        
        # 计算总长度
        total_length = max(range_info['end'] for range_info in gene_ranges.values())
        print(f"   • 序列总长度: {total_length}")
        
        # 验证输出文件
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"   • 输出行数: {len(lines)}")
            
            # 显示输出文件的前几行
            print(f"\n📄 输出文件预览（前5行）:")
            for i, line in enumerate(lines[:5], 1):
                print(f"   {i}. {line.strip()}")
            
            if len(lines) > 5:
                print(f"   ... 还有 {len(lines) - 5} 行")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        print("🧬 concat.cfg到part格式转换工具")
        print("功能: 将concat.cfg格式转换为类似Fabales.part的格式\n")
        
        success = main()
        
        if success:
            print(f"\n🎉 转换成功完成！")
            print(f"💡 提示: ANA_concat.txt文件已生成，可用于系统发育分析")
        else:
            print(f"\n⚠️ 转换失败，请检查输入文件格式")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
