# 提取系统发育树中对应各个分类的小树

# 加载必要的包
if (!require("ape", quietly = TRUE)) {
  install.packages("ape", repos = "https://cran.r-project.org/")
  library(ape)
}

# 设置工作目录
setwd("c:/Users/<USER>/Documents/augment-projects/paper")

# 读取系统发育树
cat("读取系统发育树文件...\n")
tree <- read.tree("trimmed_no_branch_tree.tre")
cat("系统发育树包含", length(tree$tip.label), "个末端节点\n")

# 显示树的一些基本信息
cat("树的末端标签（前10个）:\n")
print(head(tree$tip.label, 10))

# 定义分类文件列表
class_files <- c("ANA.txt", "monocot.txt", "dicots.txt", "superrosids.txt", "superasterids.txt")

# 为每个分类文件提取小树
for (class_file in class_files) {
  cat("\n处理文件:", class_file, "\n")
  
  # 读取分类数据
  class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
  
  # 获取该分类中的所有科名
  families_in_class <- unique(class_data$wcvp_family)
  cat("该分类包含", length(families_in_class), "个科:\n")
  print(families_in_class)
  
  # 在系统发育树中查找匹配的科名
  matching_families <- intersect(families_in_class, tree$tip.label)
  cat("在系统发育树中找到", length(matching_families), "个匹配的科:\n")
  print(matching_families)
  
  # 如果有匹配的科，使用keep.tip()提取小树
  if (length(matching_families) > 1) {
    # 使用keep.tip()函数提取包含目标科的子树
    subtree <- keep.tip(tree, matching_families)

    class_name <- gsub(".txt", "", class_file)

    # 保存提取的小树
    output_file <- paste0(class_name, "_subtree.tre")
    write.tree(subtree, output_file)

    cat("成功提取", class_name, "的小树，已保存为:", output_file, "\n")

    # 显示小树的基本统计信息
    cat("小树统计信息:\n")
    cat("  - 末端节点数:", Ntip(subtree), "\n")
    cat("  - 内部节点数:", Nnode(subtree), "\n")
    cat("  - 总分支数:", length(subtree$edge.length), "\n")

    # 验证提取的小树是否包含所有目标科
    extracted_families <- subtree$tip.label
    target_in_tree <- intersect(extracted_families, matching_families)
    cat("  - 包含目标科:", length(target_in_tree), "个\n")

    if (length(target_in_tree) == length(matching_families)) {
      cat("  ✓ 所有目标科都已包含在小树中\n")
    } else {
      missing_families <- setdiff(matching_families, extracted_families)
      cat("  ⚠ 缺失的目标科:", paste(missing_families, collapse = ", "), "\n")
    }

    # 显示小树中包含的所有科
    cat("  - 小树包含的科:", paste(sort(extracted_families), collapse = ", "), "\n")
    
  } else if (length(matching_families) == 1) {
    cat("警告: 只找到1个匹配的科，无法构建有意义的小树\n")
  } else {
    cat("警告: 没有找到匹配的科\n")
  }
  
  # 显示未匹配的科
  unmatched_families <- setdiff(families_in_class, tree$tip.label)
  if (length(unmatched_families) > 0) {
    cat("未在系统发育树中找到的科 (", length(unmatched_families), "个):\n")
    print(unmatched_families)
  }
  
  cat(rep("-", 50), "\n")
}

# 生成总结报告
cat("\n=== 处理总结 ===\n")
cat("原始系统发育树包含", length(tree$tip.label), "个科\n")

# 统计所有分类文件中的科
all_families <- c()
for (class_file in class_files) {
  class_data <- read.table(class_file, sep = "\t", header = TRUE, stringsAsFactors = FALSE)
  families_in_class <- unique(class_data$wcvp_family)
  all_families <- c(all_families, families_in_class)
}
unique_families <- unique(all_families)
cat("所有分类文件共包含", length(unique_families), "个不同的科\n")

# 统计匹配情况
matched_families <- intersect(unique_families, tree$tip.label)
cat("其中", length(matched_families), "个科在系统发育树中找到匹配\n")
cat("匹配率:", round(length(matched_families)/length(unique_families)*100, 2), "%\n")

cat("\n处理完成！\n")
